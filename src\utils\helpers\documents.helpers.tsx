import { FormikErrors } from 'formik';
import { Q1Value, FormValues } from 'models/documents';
import { SaveDocumentAccountFieldBodyRow, SaveDocumentAccountFields } from 'models/request';
import { DocumentFieldsSectionField, FieldGroup, FieldGroupColumn } from 'models/response';
import { SaveDocumentFields, SaveDocumentQ1FieldValue } from 'models/request';
import { docHeaderExcluded } from 'utils/constants';
import intlHelper from './intl.helper';

const validate = (values: FormValues) => {
  const errors: FormikErrors<FormValues> = {};
  try {
    Object.entries(values).forEach(([fieldName, field]) => {
      if (field && typeof field !== 'string' && !docHeaderExcluded.includes(fieldName)) {
        const q1Field = field as Q1Value;
        if (typeof q1Field.content[0].content === 'string' && q1Field.content[0].content.length > 2048) {
          errors[fieldName] = 'value-too-long';
        }
      }
    });
  } catch (e) {
    console.error(e);
  }
  return errors;
};

// this function takes formikValues { [key: string]: Q1Value }
// and transform them in SaveDocumentFieldValue[] for the property
// documentFields of the /saveDocument API
// header properties are excluded by a filter
const prepareQ1Fields = (
  values: FormValues,
  documentFlatFields: DocumentFieldsSectionField[],
): SaveDocumentFields[] => {
  return Object.keys(values)
    .filter((value) => !docHeaderExcluded.includes(value))
    .map((value) => {
      const { position = 0, idField = 0 } = documentFlatFields.find(({ fieldName }) => fieldName === value) || {};
      const field = values[value] as Q1Value;
      const { content } = field;
      const Q1fieldValue: SaveDocumentQ1FieldValue[] = content.map((ele) => {
        return {
          content: ele.content,
          boundingBox: { ...ele.boundingBox },
          pageNumber: ele.pageNumber,
          confidence: ele.confidence,
          status: ele.status,
        };
      });
      return {
        idField,
        values: Q1fieldValue,
        position,
      };
    });
};

// this function takes formikValues { [key: string]: Q1Value }
// and transforms them in SaveDocumentFieldValue[] for the property
// documentFields of the /saveDocument API
// header properties are excluded by a filter
const prepareQ3Fields = (
  values: FormValues,
  documentFlatFields: DocumentFieldsSectionField[],
): SaveDocumentAccountFields => {
  const accountFields: SaveDocumentAccountFields = {};
  // which fields have a body
  const withBodyFields = Object.keys(values)
    .filter((value) => !docHeaderExcluded.includes(value))
    .filter((value) => {
      const field = values[value] as Q1Value;
      return field.body !== null;
    });

  withBodyFields.forEach((value) => {
    const idFieldGroup = documentFlatFields.find(({ fieldName }) => fieldName === value)?.idFieldGroup;
    const field = values[value] as Q1Value;
    const { body } = field;
    if (idFieldGroup && body) {
      // sort back to original row order (bypass sorting filter)
      const bodyQ3 = [...body];
      bodyQ3.sort((a, b) => Number(a.id) - Number(b.id));
      const saveDocumentAccountFieldBodyRow: SaveDocumentAccountFieldBodyRow[] = bodyQ3.map((row) => {
        const saveDocumentAccountFieldBodyRow: SaveDocumentAccountFieldBodyRow = {};
        const idsColumns = Object.keys(row);
        idsColumns.forEach((idColum) => {
          if (idColum !== 'id') {
            saveDocumentAccountFieldBodyRow[idColum] = {
              content: row[idColum].content,
              boundingBox: {
                x1: row[idColum]?.boundingBox.x1,
                y1: row[idColum]?.boundingBox.y1,
                x2: row[idColum]?.boundingBox.x2,
                y2: row[idColum]?.boundingBox.y2,
              },
              pageNumber: row[idColum].pageNumber,
              confidence: row[idColum].confidence,
            };
          }
        });
        return saveDocumentAccountFieldBodyRow;
      });
      accountFields[`${idFieldGroup}`] = saveDocumentAccountFieldBodyRow;
    }
  });
  return accountFields;
};

const setInitialBodyData = (bodyRows: FieldGroup['tableValues'], columnsBody: FieldGroupColumn[], decimal?: number) => {
  return bodyRows.map((ele, index) => {
    const obj = Object.fromEntries(
      Object.entries(ele).map(([key, val]) => {
        const currencyCell = columnsBody?.find(
          ({ code, type, dataType }) => code.toString() === key && type === 3 && dataType === 0,
        );
        return [
          key,
          {
            confidence: val.confidence,
            content: currencyCell
              ? val.content.length === 0
                ? ''
                : intlHelper.formatNumber(intlHelper.convertToNumber(val.content), decimal, ',')
              : val.content,
            pageNumber: val.pageNumber,
            boundingBox: { x1: val.x1, y1: val.y1, x2: val.x2, y2: val.y2 },
          },
        ];
      }),
    );
    // @ts-ignore
    obj.id = index.toString();
    return obj;
  });
};

const isOpenDocumentView = () => window.location.href.split('/').pop() === 'edit';

export default {
  validate,
  prepareQ3Fields,
  prepareQ1Fields,
  setInitialBodyData,
  isOpenDocumentView,
};
