import { SummaryKpiFilters, CategoryDetailsFilters, ChartFilters } from 'models/request';
import services from 'services';
import utils from 'utils';
import { CHART_TYPE } from 'utils/constants';
import { colorPalette } from 'utils/styleConstants';

export const getKpiSummary = async (params: SummaryKpiFilters) => {
  try {
    if (params.dateFrom && params.dateTo) {
      const { data } = await services.getSummary(params);
      return data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching KPI summary:', error);
    return [];
  }
};

export const getGbsCompanies = async () => {
  try {
    const { data } = await services.getGbsCompanies();
    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const getDocumentTypes = async () => {
  try {
    const { data } = await services.getDocumentTypes();
    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const getUserCompanies = async (idUser: number, idProgram: number) => {
  try {
    const { data } = await services.getConfigurationsByCompanies(idUser, idProgram);
    return data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const getCategoryDetails = async (params: CategoryDetailsFilters) => {
  try {
    if (params.dateFrom && params.dateTo) {
      const { data } = await services.getCategoryDetails(params);

      return data;
    }
    return [];
  } catch (error) {
    console.error('Error fetching category details:', error);
    return [];
  }
};

export const getCharts = async (params: ChartFilters) => {
  try {
    if (params.dateFrom && params.dateTo) {
      const { line, pie } = CHART_TYPE;

      const { data: chartDataArray } = await services.getCharts(params);

      return chartDataArray.map((chartData) => {
        if (chartData.chartType.toLowerCase() === line) {
          // generate colors for every dataset; exclude red, red is reserved for average line
          const colors = utils.colors.generateHexColors({
            numColors: chartData.series.length,
            hueRangeStart: 30,
            hueRangeEnd: 285,
          });

          return {
            ...chartData,
            series: chartData.series.map((dataset, index) => ({
              ...dataset,
              color: colors[index] || colorPalette.grey.grey7,
            })),
          };
        }

        if (chartData.chartType.toLowerCase() === pie) {
          return {
            ...chartData,
            series: chartData.series.map((dataset) => {
              // Generate colors for every pair of data inside a series
              const colors = utils.colors.generateHexColors({
                numColors: dataset.data.length,
                hueRangeStart: 30,
                hueRangeEnd: 285,
              });

              const total = dataset.data.reduce((acc, item) => {
                const num = Number(item.y);
                return !isNaN(num) ? acc + num : acc;
              }, 0);

              return {
                ...dataset,
                data: dataset.data.map((value, index) => {
                  const num = Number(value.y);
                  const percentage = !isNaN(num) && total !== 0 ? (num / total) * 100 : 0;
                  return {
                    ...value,
                    color: colors[index],
                    percentage: parseFloat(percentage.toFixed(2)),
                  };
                }),
              };
            }),
          };
        }

        return chartData;
      });
    }

    return [];
  } catch (error) {
    console.error('Error fetching category details:', error);
    return [];
  }
};
