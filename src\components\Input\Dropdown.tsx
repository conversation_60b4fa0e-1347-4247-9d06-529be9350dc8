/* eslint-disable max-lines */
// https://react-select.com/home#getting-started
import React from 'react';
import Select, { MenuPlacement, Styles } from 'react-select';
import Creatable from 'react-select/creatable';
import { fontSizePalette, fontWeightPalette, zIndexPalette } from 'utils/styleConstants';
import iconClear from 'images/lucy4/icon_x.svg';
import styled from 'styled-components/macro';
import { useTheme } from 'providers/ThemeProvider';

export interface DropdownProps {
  id?: string;
  options: any[];
  isMulti?: boolean;
  disabled?: boolean;
  placeholder?: string;
  small?: boolean;
  onChange?: (option: any, info?: any) => void;
  onCreateOption?: (option: any) => void;
  onFocus?: () => void;
  value?: any;
  isClearable?: boolean;
  name?: string;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  feedbackColor?: string;
  noBorder?: boolean;
  defaultDropdownValue?: any;
  creatable?: boolean;
  isPortal?: boolean;
  customHeight?: string;
  menuIsOpen?: boolean;
  minWidthLabel?: string;
  backgroundColor?: string;
  fontSize?: string;
  valueContainerPadding?: string;
  singleValueMargin?: string;
  margin?: string;
  menuWidth?: string;
  width?: string;
  displayWrapper?: string;
  overflowWidth?: string;
  closeMenuOnScroll?: EventListener;
  setRef?: any;
  onInputChange?: (val: string) => void;
}

const SelectWrapper = styled.div<{ display?: string }>`
  position: relative;
  ${({ display }) => display && `display:${display}`};
`;
const FeedbackText = styled.p<{ feedbackColor: string; maxWidth?: string }>`
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor }) => feedbackColor};
  margin: 3px 5px 0 3px;
  text-align: right;
  position: relative;
  ${({ maxWidth }) => maxWidth && `max-width: ${maxWidth}`};
`;

const Dropdown = (props: DropdownProps) => {
  const { isDarkMode, theme: appTheme } = useTheme(); // renamed to appTheme to avoid confusion
  const {
    id = '',
    options,
    isMulti,
    disabled,
    placeholder,
    onChange,
    onCreateOption,
    onFocus,
    small,
    isClearable,
    value,
    name,
    hasFeedback,
    feedbackMessage,
    feedbackColor = 'red',
    noBorder,
    defaultDropdownValue,
    creatable,
    menuIsOpen,
    isPortal = true,
    customHeight,
    minWidthLabel = small ? '' : '165px',
    backgroundColor,
    fontSize,
    valueContainerPadding,
    singleValueMargin,
    margin,
    menuWidth = 'fit-content',
    width,
    displayWrapper,
    overflowWidth,
    closeMenuOnScroll,
    setRef,
    onInputChange,
  } = props;

  const commonStyles: Styles = {
    container: (provided) => ({
      ...provided,
      width: `${width}` || provided.width,
      margin: `${margin}` || provided.margin,
    }),
    control: (provided) => ({
      ...provided,
      fontSize: fontSize || fontSizePalette.body.XS,
      fontWeight: fontWeightPalette.light,
      minWidth: minWidthLabel,
      backgroundColor: disabled
        ? isDarkMode
          ? appTheme.colorPalette.grey.grey2
          : appTheme.colorPalette.grey.grey3
        : backgroundColor
        ? backgroundColor
        : isDarkMode
        ? appTheme.colorPalette.grey.grey3
        : appTheme.colorPalette.white,
      border:
        hasFeedback && feedbackColor
          ? `1px solid ${feedbackColor}`
          : noBorder
          ? 'none'
          : disabled
          ? 'none'
          : `1px solid ${appTheme.colorPalette.grey.grey3}`,
      opacity: disabled ? 0.7 : 1,
    }),
    valueContainer: (provided) => ({
      ...provided,
      color: disabled
        ? appTheme.colorPalette.grey.grey12
        : isDarkMode
        ? appTheme.colorPalette.grey.grey2
        : appTheme.colorPalette.grey.grey9,
        opacity: disabled ? 0.7 : 1,
    }),
    clearIndicator: (provided) => ({
      ...provided,
      padding: 0,
      marginRight: '2px',
      marginBottom: '1px',
      color: 'grey',
      width: '9px',
      height: '9px',
      content: `url(${iconClear})`,
    }),
    menuList: (provided) => ({
      ...provided,
      backgroundColor: isDarkMode ? appTheme.colorPalette.grey.grey3 : appTheme.colorPalette.white,
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: isDarkMode ? appTheme.colorPalette.grey.grey3 : appTheme.colorPalette.white,
      zIndex: zIndexPalette.high,
      width: `${menuWidth}`,
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected
        ? isDarkMode
          ? appTheme.colorPalette.turquoise.dark
          : appTheme.colorPalette.turquoise.normal
        : state.isFocused
        ? isDarkMode
          ? appTheme.colorPalette.grey.grey8
          : appTheme.colorPalette.turquoise.background
        : isDarkMode
        ? appTheme.colorPalette.grey.grey3
        : appTheme.colorPalette.white,
      color: state.isSelected
        ? appTheme.colorPalette.white
        : appTheme.colorPalette.black,
      justifyContent: 'flex-start',
      display: 'flex',
      alignContent: 'center',
    }),
    placeholder: (provided) => ({
      ...provided,
      color: isDarkMode ? appTheme.colorPalette.black : appTheme.colorPalette.grey.grey9,
      fontSize: fontSize || fontSizePalette.body.XS,
      fontWeight: fontWeightPalette.light,
    }),
    singleValue: (provided) => ({
      ...provided,

      margin: singleValueMargin || provided.margin,
      fontWeight: fontWeightPalette.regular,
      maxWidth: `calc(100% - ${overflowWidth || '5px'})`,
      color: disabled
        ? isDarkMode
          ? appTheme.colorPalette.grey.grey8
          : appTheme.colorPalette.grey.grey12
        : isDarkMode
        ? appTheme.colorPalette.grey.grey10
        : appTheme.colorPalette.black,
    }),
  };

  const smallStyle: Styles = {
    container: commonStyles.container,
    control: (provided, state) => ({
      ...commonStyles.control?.(provided, state),
      minHeight: 18,
      height: 18,
      alignItems: 'unset',
    }),
    valueContainer: (provided, state) => ({
      ...commonStyles.valueContainer?.(provided, state),
      height: 18,
      padding: '0 8px',
      fontSize: fontSizePalette.body.XXS,
      position: 'static',
    }),
    indicatorSeparator: () => ({
      display: 'none',
    }),
    clearIndicator: commonStyles.clearIndicator,
    dropdownIndicator: (provided) => ({
      ...provided,
      padding: 0,
    }),
    menuList: commonStyles.menuList,
    menu: commonStyles.menu,
    noOptionsMessage: (provided) => ({
      ...provided,
      fontSize: fontSizePalette.body.XXS,
    }),
    option: (provided, state) => ({
      ...commonStyles.option?.(provided, state),
      fontSize: fontSizePalette.body.XXS,
      borderRadius: '3px',
    }),
    singleValue: (provided, state) => ({
      ...commonStyles.singleValue?.(provided, state),
    }),
  };

  const customStyles: Styles | undefined = small
    ? smallStyle
    : {
        container: commonStyles.container,
        control: (provided, state) => ({
          ...commonStyles.control?.(provided, state),
          minHeight: '24px',
          height: customHeight || '25px',
        }),
        valueContainer: (provided) => ({
          ...provided,
          height: customHeight || '25px',
          padding: valueContainerPadding || '0 6px',
        }),
        singleValue: (provided, state) => ({
          ...commonStyles.singleValue?.(provided, state),
        }),
        input: (provided) => ({
          ...provided,
          margin: '0px',
          color: appTheme.colorPalette.black,
        }),
        indicatorSeparator: () => ({
          display: 'none',
        }),
        menuList: commonStyles.menuList,
        menu: commonStyles.menu,
        indicatorsContainer: (provided) => ({
          ...provided,
          height: customHeight || '25px',
          color: isDarkMode ? appTheme.colorPalette.white : undefined,
        }),
        clearIndicator: commonStyles.clearIndicator,
        noOptionsMessage: (provided) => ({
          ...provided,
          fontSize: fontSizePalette.body.XS,
        }),
        dropdownIndicator: (provided) => ({
          ...provided,
          padding: '2px',
        }),
        option: (provided, state) => ({
          ...commonStyles.option?.(provided, state),
          fontSize: fontSizePalette.body.XS,
          fontWeight: fontWeightPalette.regular,
        }),
        placeholder: commonStyles.placeholder,
      };

  const propsComponent = {
    name: name,
    className: 'react-select',
    styles: customStyles,
    isClearable: isClearable,
    onInputChange: onInputChange,
    onChange: onChange,
    onCreateOption: onCreateOption,
    onFocus: onFocus,
    value: value,
    theme: (selectTheme: any) => ({
      ...selectTheme,
      borderRadius: small ? 9 : 5,
      colors: {
        ...selectTheme.colors,
        primary25: isDarkMode ? appTheme.colorPalette.grey.grey8 : appTheme.colorPalette.turquoise.background,
        primary: feedbackColor && hasFeedback ? feedbackColor : appTheme.colorPalette.turquoise.normal,
        neutral10: isDarkMode ? appTheme.colorPalette.grey.grey8 : appTheme.colorPalette.turquoise.background,
        neutral30: feedbackColor && hasFeedback ? feedbackColor : undefined,
        neutral80: isDarkMode ? appTheme.colorPalette.white : appTheme.colorPalette.black,
      },
    }),
    isMulti: isMulti,
    isDisabled: disabled,
    options: options,
    placeholder: placeholder,
    defaultValue: defaultDropdownValue,
    menuIsOpen,
    menuPlacement: 'auto' as MenuPlacement,
    tabSelectsValue: false,
  };

  return (
    <SelectWrapper display={displayWrapper}>
      {creatable ? (
        <Creatable {...propsComponent} />
      ) : isPortal ? (
        <Select
          ref={setRef}
          key={`select_${id}_${value}`}
          id={id}
          menuPortalTarget={document.getElementById('react-select-portal')}
          closeMenuOnScroll={closeMenuOnScroll}
          {...propsComponent}
        />
      ) : (
        <Select
          key={`select_${id}_${value}`}
          id={id}
          ref={setRef}
          closeMenuOnScroll={closeMenuOnScroll}
          {...propsComponent}
        />
      )}
      {hasFeedback && (
        <FeedbackText feedbackColor={feedbackColor} maxWidth={minWidthLabel}>
          {feedbackMessage}
        </FeedbackText>
      )}
    </SelectWrapper>
  );
};

export default Dropdown;
