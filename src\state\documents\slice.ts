import {
  ActiveBox,
  DocumentsTable,
  Layout,
  DocumentsState,
  FieldType,
  EditDocuments,
  focussedCell,
  PredictionColumn,
} from 'models/documents';
import { PayloadAction, createSlice } from '@reduxjs/toolkit';
import { Row } from 'models/table';
import {
  DocumentHeaderResponse,
  FieldGroupColumn,
  AIsuggestion,
  Column,
  DocumentFields,
  MovType,
  Template,
  UpdateTableRowsPayload,
  ITemplateSavedFilters,
} from 'models/response';
import { TableState } from 'react-table';
import { IFilterValue } from 'models/request';
import { DataGridRow } from 'components/DataGrid';

export const initialStateEditDocuments: EditDocuments = {
  balanceField: null,
  columnsBody: null,
  nameBody: null,
  documentFields: null,
  documentDecimal: 2,
  documentHeader: null,
  showBody: false,
  focussedQ3Cell: null,
  headerName: null,
  fieldGroupId: null,
  isDocReadOnly: false,
  ocr: {
    q1: {
      fieldInputType: 0 as FieldType,
      name: '',
      multi: false,
      index: 0,
    },
  },
  predictionsData: [],
  formattedPredictionsData: [],
  selectedRowPrediction: null,
};

export const initialState: DocumentsState = {
  layout: 'rows',
  edit: initialStateEditDocuments,
  templates: [],
  activeTemplate: null,
  activeDocumentId: [],
  documentsTable: {
    isAllAuth: false,
    columns: [],
    rows: [],
    filters: [],
    sortBy: [],
    fixedColumns: 0,
    pageSize: 10,
    currentPage: 0,
    filteredDocument: [],
  },
  documentImage: '',
  documentImageId: 0,
  // all ai box suggestions, state not used at the moment
  aiSuggestions: [],
  activeBox: null,
  importedDocData: null,
  isSearchFilterVisible: true,
  documentSearchFormValue: [],
  isJiesRunning: 'yellow',
  engineInfo: null,
  selectedTemplateFilter: null,
};

export const slice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    setDefaultState: () => initialState,
    setDefaultEditState: (state) => {
      state.edit = initialStateEditDocuments;
      state.activeBox = null;
      state.aiSuggestions = [];
    },
    setDocumentGridColumns: (state, action: PayloadAction<Column[]>) => {
      state.documentsTable.columns = action.payload;
    },

    setDocumentGridRows: (state, { payload }: PayloadAction<Row[]>) => {
      state.documentsTable.rows = payload;
    },
    setDocumentGridFixedColumns: (state, { payload }: PayloadAction<number>) => {
      state.documentsTable.fixedColumns = payload;
    },
    setDocumentImage: (state, { payload }: PayloadAction<string>) => {
      state.documentImage = payload;
    },
    setDocumentImageId: (state, { payload }: PayloadAction<number>) => {
      state.documentImageId = payload;
    },
    setTablePageSize: (state, { payload }: PayloadAction<number>) => {
      state.documentsTable.pageSize = payload;
    },
    setCurrentFilters: (state, { payload }: PayloadAction<TableState['filters']>) => {
      state.documentsTable.filters = payload;
    },
    setCurrentSorting: (state, { payload }: PayloadAction<DocumentsTable['sortBy']>) => {
      state.documentsTable.sortBy = payload;
    },
    setCurrentPageIndex: (state, { payload }: PayloadAction<DocumentsTable['currentPage']>) => {
      state.documentsTable.currentPage = payload;
    },
    setActiveDocumentId: (state, { payload }: PayloadAction<number[]>) => {
      state.activeDocumentId = payload;
    },
    setTemplates: (state, { payload }: PayloadAction<Template[]>) => {
      state.templates = payload;
    },
    setActiveTemplateId: (state, { payload }: PayloadAction<number | null>) => {
      state.activeTemplate = payload;
    },
    setIsAllAuth: (state, { payload }: PayloadAction<boolean>) => {
      state.documentsTable.isAllAuth = payload;
    },
    setDocumentHeader: (state, { payload }: PayloadAction<DocumentHeaderResponse | null>) => {
      state.edit.documentHeader = payload;
    },
    setActualHMoveType: (state, { payload }: PayloadAction<MovType | null>) => {
      if (state.edit.documentHeader) {
        state.edit.documentHeader.actualHMoveType = payload;
      }
    },
    removeRows: (state, { payload }: PayloadAction<number[]>) => {
      const newRows = state.documentsTable.rows.filter((row) => !payload.includes(row.protocol));
      state.documentsTable.rows = newRows;
    },
    updateTableRows: (state, { payload }: PayloadAction<UpdateTableRowsPayload>) => {
      // update rows based on property
      const newRows = state.documentsTable.rows.map((row: Row) => {
        if (payload.protocols.includes(row.protocol)) {
          row[payload.property] = payload.value;
        }
        return row;
      });
      state.documentsTable.rows = newRows;
    },
    setBodyColumns: (state, { payload }: PayloadAction<FieldGroupColumn[] | null>) => {
      state.edit.columnsBody = payload;
    },
    setBalanceField: (state, { payload }: PayloadAction<string | null>) => {
      state.edit.balanceField = payload;
    },
    setBodyName: (state, { payload }: PayloadAction<string | null>) => {
      state.edit.nameBody = payload;
    },
    setShowBody: (state, { payload }: PayloadAction<boolean>) => {
      state.edit.showBody = payload;
    },
    setDocumentFields: (state, { payload }: PayloadAction<DocumentFields>) => {
      state.edit.documentFields = payload;
    },
    setDocumentDecimal: (state, { payload }: PayloadAction<number>) => {
      state.edit.documentDecimal = payload;
    },
    setAiSuggestions: (state, { payload }: PayloadAction<AIsuggestion[]>) => {
      state.aiSuggestions = payload;
    },
    setActiveBox: (state, { payload }: PayloadAction<ActiveBox | null>) => {
      state.activeBox = payload;
    },
    setQ3Cell: (state, { payload }: PayloadAction<focussedCell>) => {
      state.edit.focussedQ3Cell = payload;
    },
    setLayout: (state, { payload }: PayloadAction<Layout>) => {
      state.layout = payload;
    },
    setHeaderNameQ3: (state, { payload }: PayloadAction<string | null>) => {
      state.edit.headerName = payload;
    },
    setFocussedQ1: (state, { payload }: PayloadAction<EditDocuments['ocr']['q1']>) => {
      state.edit.ocr.q1 = payload;
    },
    setIsJiesRunning: (state, action) => {
      state.isJiesRunning = action.payload;
    },
    setEngineInfo: (state, action) => {
      state.engineInfo = action.payload;
    },
    setIsSearchFilterVisible: (state, { payload }: PayloadAction<boolean>) => {
      state.isSearchFilterVisible = payload;
    },
    setDocumentFilterForm: (state, { payload }: PayloadAction<IFilterValue[]>) => {
      state.documentSearchFormValue = payload;
    },
    setResetDocumentFilterForm: (state) => {
      state.documentSearchFormValue = [];
    },
    setFieldGroupId: (state, { payload }: PayloadAction<number | null>) => {
      state.edit.fieldGroupId = payload;
    },
    setFilteredDocFromList: (state, { payload }: PayloadAction<any>) => {
      state.documentsTable.filteredDocument = payload;
    },
    setIsDocReadOnly: (state, { payload }: PayloadAction<boolean>) => {
      state.edit.isDocReadOnly = payload;
    },
    setPredictionsData: (state, { payload }: PayloadAction<PredictionColumn[]>) => {
      state.edit.predictionsData = payload;
    },
    setFormattedPredictionsData: (state, { payload }: PayloadAction<DataGridRow[]>) => {
      state.edit.formattedPredictionsData = payload;
    },
    setSelectedPredictionsRow: (state, { payload }: PayloadAction<DataGridRow | null>) => {
      state.edit.selectedRowPrediction = payload;
    },
    setSelectedTemplateFilter: (state, { payload }: PayloadAction<ITemplateSavedFilters | null>) => {
      state.selectedTemplateFilter = payload;
    },
  },
});

export default slice.reducer;
