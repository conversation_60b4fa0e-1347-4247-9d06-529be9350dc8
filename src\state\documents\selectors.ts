import { RootState } from 'models';
import { DocumentFieldsSectionField } from 'models/response';
import { createSelector } from '@reduxjs/toolkit';

const selectDocumentsState = (state: RootState) => state.documents;
const selectTableColumns = createSelector(selectDocumentsState, (state) => state.documentsTable.columns);

const selectDocumentImage = (state: RootState) => state.documents.documentImage;

const selectDocumentImageId = (state: RootState) => state.documents.documentImageId;

const selectTableRows = (state: RootState) => state.documents.documentsTable.rows;

// all active documents id (multiselection)
const selectAllActiveDocumentsID = (state: RootState) => state.documents.activeDocumentId;

const selectAllProtocolsOfRowSelected = createSelector(selectTableRows, selectAllActiveDocumentsID, (rows, activeIDs) =>
  rows.filter(({ protocol }) => activeIDs.includes(protocol)),
);

// Assuming that the last in id (tail of the stack) is the most recent and active one
const selectActiveDocumentID = createSelector(selectAllActiveDocumentsID, (activeIDs) =>
  activeIDs?.length ? activeIDs[activeIDs.length - 1] : null,
);

const selectAllActiveRows = createSelector(selectTableRows, selectAllActiveDocumentsID, (rows, activeIDs) =>
  rows.filter(({ protocol }) => activeIDs.includes(protocol)),
);

const selectLayout = (state: RootState) => state.documents.layout;

const selectTableFixedColumns = (state: RootState) => state.documents.documentsTable.fixedColumns;

const selectTablePageSize = (state: RootState) => state.documents.documentsTable.pageSize;

const selectTemplates = (state: RootState) => state.documents.templates;

const selectActiveTemplateId = (state: RootState) => state.documents.activeTemplate;

const selectIsAllAuth = (state: RootState) => state.documents.documentsTable.isAllAuth;

const selectDocumentHeader = (state: RootState) => state.documents.edit.documentHeader;

const selectIdSubject = (state: RootState) => state.documents.edit.documentHeader?.subject?.idWSubject;

const selectDocumentFields = (state: RootState) => state.documents.edit.documentFields;

const selectDocumentDecimal = (state: RootState) => state.documents.edit.documentDecimal;

const selectAvailableHMoveTypes = (state: RootState) => state.documents.edit.documentHeader?.availableHMoveTypes;

// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectDocumentFlatFields = createSelector(
  selectDocumentFields,
  (documentFields) =>
    documentFields?.reduce((acc: DocumentFieldsSectionField[], ele) => [...acc, ...ele.sectionFields], []) || [],
);

// returning active document
// Assuming that the last in id (head of the stack) is the most recent and active one
// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectActiveDocument = createSelector(selectActiveDocumentID, selectTableRows, (activeID, rows) =>
  rows.find(({ protocol }) => protocol === activeID),
);

// return all selected documents
// Assuming that the last in id (head of the stack) is the most recent and active one
// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectAllActiveDocuments = createSelector(selectAllActiveDocumentsID, selectTableRows, (activeIDs, rows) =>
  rows.filter(({ protocol }) => activeIDs.includes(protocol)),
);

const selectBodyColumns = (state: RootState) => state.documents.edit.columnsBody;

const selectBalanceField = (state: RootState) => state.documents.edit.balanceField;

const selectBodyName = (state: RootState) => state.documents.edit.nameBody;

const selectSuggestions = (state: RootState) => state.documents.aiSuggestions;

const selectActiveBox = (state: RootState) => state.documents.activeBox;

const selectShowBody = (state: RootState) => state.documents.edit.showBody;

const selectIdFieldGroup = (state: RootState) => state.documents.edit.fieldGroupId;

const selectFocussedQ3Cell = createSelector(
  (state: RootState) => state.documents.edit.focussedQ3Cell,
  (q3) => q3,
);

const selectIsTemplateReadOnly = (state: RootState) => {
  const activeIdTemplate = selectActiveTemplateId(state);
  const templates = selectTemplates(state);
  return templates.find((el) => el.idTemplate === activeIdTemplate)?.readOnly;
};

const selectIsTemplateArchived = (state: RootState) => {
  const activeIdTemplate = selectActiveTemplateId(state);
  const templates = selectTemplates(state);
  return templates.find((el) => el.idTemplate === activeIdTemplate)?.archive;
};

// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectTemplateActionsList = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) =>
    // eslint-disable-next-line camelcase
    templates.find((el) => el.idTemplate === activeIdTemplate)?.actions_document_list,
);

// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectTemplateActionsOpen = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) =>
    // eslint-disable-next-line camelcase
    templates.find((el) => el.idTemplate === activeIdTemplate)?.actions_document_open,
);

const selectHeaderName = (state: RootState) => state.documents.edit.headerName;

const selectActiveFilters = (state: RootState) => state.documents.documentsTable.filters;

const selectCurrentSorting = (state: RootState) => state.documents.documentsTable.sortBy;

const selectCurrentPageIndex = (state: RootState) => state.documents.documentsTable.currentPage;

const selectIsSearchFilterVisible = (state: RootState) => state.documents.isSearchFilterVisible;

const selectFocussedQ1Cell = createSelector(
  (state: RootState) => state.documents.edit.ocr.q1,
  (q1) => q1,
);

const selectIsJiesRunning = (state: RootState) => state.documents.isJiesRunning;

const selectEngineInfo = (state: RootState) => state.documents.engineInfo;

const selectDocumentSearchFormValues = (state: RootState) => state.documents.documentSearchFormValue;

const selectFilteredDocFromList = (state: RootState) => state.documents.documentsTable.filteredDocument;

const selectIsDocReadOnly = (state: RootState) => state.documents.edit.isDocReadOnly;

const selectPredictionsData = (state: RootState) => state.documents.edit.predictionsData;

const selectFormattedPredictionsData = (state: RootState) => state.documents.edit.formattedPredictionsData;

const setSelectedPredictionRow = (state: RootState) => state.documents.edit.selectedRowPrediction;

const selectedTemplateFilter = (state: RootState) => state.documents.selectedTemplateFilter;

export default {
  selectTableColumns,
  selectDocumentImage,
  selectDocumentImageId,
  selectActiveDocument,
  selectAllActiveDocuments,
  selectAllActiveDocumentsID,
  selectActiveDocumentID,
  selectTableRows,
  selectTableFixedColumns,
  selectTablePageSize,
  selectActiveTemplateId,
  selectTemplates,
  selectIsAllAuth,
  selectDocumentFields,
  selectDocumentDecimal,
  selectDocumentFlatFields,
  selectBodyColumns,
  selectSuggestions,
  selectActiveBox,
  selectShowBody,
  selectBodyName,
  selectDocumentHeader,
  selectIsTemplateReadOnly,
  selectIsTemplateArchived,
  selectFocussedQ3Cell,
  selectLayout,
  selectHeaderName,
  selectTemplateActionsList,
  selectTemplateActionsOpen,
  selectActiveFilters,
  selectCurrentSorting,
  selectBalanceField,
  selectCurrentPageIndex,
  selectIsSearchFilterVisible,
  selectFocussedQ1Cell,
  selectIsJiesRunning,
  selectEngineInfo,
  selectDocumentSearchFormValues,
  selectIdSubject,
  selectAvailableHMoveTypes,
  selectIdFieldGroup,
  selectAllActiveRows,
  selectFilteredDocFromList,
  selectIsDocReadOnly,
  selectFormattedPredictionsData,
  setSelectedPredictionRow,
  selectPredictionsData,
  selectAllProtocolsOfRowSelected,
  selectedTemplateFilter,
};
