import Box from 'components/Box';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import utils from 'utils';
import action from 'state/actions';
import { modalActionType } from 'utils/constants';
import Modal from 'components/Modal';
import selectors from 'state/selectors';
import Dropdown from 'components/Input/Dropdown';
import { ITemplateSavedFilters } from 'models/response';
import actions from 'state/actions';
import { IFilterValue } from 'models/request';
import ModalsContent from '../../ModalsContent/ListModalsContent';
import { SelectTemplateActions } from 'routes/Documents/routes/documentList';

interface Props {
  list: ITemplateSavedFilters[];
  selected: ITemplateSavedFilters | null;
  filterValues: IFilterValue[];
  selectTemplateActions: SelectTemplateActions;
  onResetFilters: () => void;
}

const SelectTemplateFilters = (props: Props) => {
  const { list, selected, filterValues, selectTemplateActions, onResetFilters } = props;

  const { onDeleteTemplate, onSaveTemplate } = selectTemplateActions;

  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const isModalVisible = useSelector(selectors.modal.getVisibility);

  const closeModal = () => dispatch(action.modal.closeModal());

  const openDeleteModalFilters = () => {
    dispatch(
      action.modal.openModal(modalActionType.documents.DELETE_PREFERENCE_FILTER, {
        title: t('delete_confirmation_template'),
        subtitle: t('sure_delete_preference_filters'),
        func: onDeleteTemplate,
      }),
    );
  };

  const saveFilters = () => {
    dispatch(
      action.modal.openModal(modalActionType.documents.CREATE_UPDATE_TEMPLATE_FILTER, {
        selected,
        onSaveTemplate,
        filterValues,
      }),
    );
  };

  const style = { display: 'flex', alignItems: 'center', justifyContent: 'center', height: '60px' };

  return (
    <div style={{ width: '70%', margin: '40px auto' }}>
      <Box
        title={t('select-filters')}
        hasButton={true}
        buttonsProperty={[
          {
            buttonAction: openDeleteModalFilters,
            buttonLabel: t('delete-preference-filters'),
            isButtonActive: !!selected,
          },
          {
            buttonAction: saveFilters,
            buttonLabel: t(selected ? 'update-filters' : 'save-filters'),
            isButtonActive: filterValues.length > 0,
          },
        ]}
        visible={true}
        style={style}
      >
        <Dropdown
          onChange={(val) => {
            dispatch(actions.documents.setResetDocumentFilterForm());
            if (!val?.value) {
              dispatch(actions.documents.setSelectedTemplateFilter(null));
              return;
            }

            const selected = list?.find((el) => el.idSavedFilter === val.value);
            dispatch(actions.documents.setSelectedTemplateFilter(selected ?? null));
            onResetFilters();
          }}
          placeholder={t('select_filter_template')}
          value={selected && { label: selected?.description, value: selected.idSavedFilter }}
          options={utils.input.buildOptions(list || [], 'description', 'idSavedFilter')}
          isClearable
          width="165px"
        />
      </Box>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={true}>
        <ModalsContent />
      </Modal>
    </div>
  );
};
export default SelectTemplateFilters;
