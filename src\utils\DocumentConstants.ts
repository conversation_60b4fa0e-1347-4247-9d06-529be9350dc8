/* eslint-disable max-lines */
import { TableState } from 'react-table';
import {
  Template,
  DocumentHeaderResponse,
  DocumentType,
  DocumentFieldsSectionField,
  HTableValues,
  AIsuggestion,
  DocumentFieldsSection,
  BoundingBox,
  Column,
  TableColumn,
  FieldGroupColumn,
  SubjectDocHeader,
} from 'models/response';
import { Row } from 'models/table';
import { ActiveBox, EditDocuments, focussedCell } from 'models/documents';

export const currentFilters: TableState['filters'] = [
  {
    id: '1',
    value: '1',
  },
  {
    id: '2',
    value: '2',
  },
];
export const currentSort: TableState['sortBy'] = [
  {
    id: '1',
    desc: false,
  },
];
export const currentTemplate: Template[] = [
  {
    idTemplate: 1,
    description: 'test',
    fixedColumns: 0,
    readOnly: false,
    archive: false,
    // eslint-disable-next-line @typescript-eslint/camelcase
    actions_document_list: ['1', '2'],
    // eslint-disable-next-line @typescript-eslint/camelcase
    actions_document_open: ['1', '3'],
    subViews: [],
    customFilter: '',
    customLabel: '',
    dynamicFilters: false,
    hideAllDocumentsDropdown: false,
    showSavedFilters: false,
  },
];
export const company = {
  idCompany: 6,
  name: 'nesciunt',
  label: 'Qui ut minus eligendi quia qui.',
  vatNumber: 'Omnis rem porro tempora voluptatem error est minus dicta.',
  socialReason: 'Et doloribus sapiente placeat soluta.',
  fiscalCode: 'Provident placeat eos dicta.',
  phone: 'ut inventore illo',
  fax: 'Quos autem ut eum earum elig',
  email: '<EMAIL>',
  url: 'Maiores perspiciatis  voluptate.',
  onlineCode: 'Vitae  Voluptas inventore itaque.',
  referenceUser: 10,
  counterPrefix: 'Id  reprehenderit voluptas molestias.',
  country: 'ipsam animi minima',
  currency: '',
  countryCurrency: '',
};
export const documentType: DocumentType = {
  idDocType: 1,
  docName: 'Molestiae saepe recusandae mollitia sunt odit.',
  fixedName: 'molestiae',
  batchDistribute: 18,
  docType: 42,
  creditDebit: 48,
  genericOption: false,
  ocr: false,
  hide: true,
};
export const docTypeArray: DocumentType[] = [documentType];
export const move = {
  changeSign: true,
  code: 'Impedit pariatur nostrum sed commodi adipisci et ab qui.',
  description: 'dolores aliquam quos',
  idCompany: 12,
  q3: false,
};
export const subjectVal: SubjectDocHeader = {
  idWSubject: 20,
  subject: 'Est reprehenderit esse rem dolorum aut iure quidem.',
  vatNumber: 'Tempore consequatur est.',
  date: 'Vel modi placeat rerum explicabo inventore sequi.',
  fiscalCode: 'Dolores est id qui dignissimos corporis sed minus.',
  tel: 'Ullam inventore repellendus est expedita accusantium.',
  fax: 'Enim exercitationem officia et nesciunt.',
  email: 'Mollitia id error cum magni nihil ut velit.',
  url: 'Fuga velit quod aut distinctio sed.',
  status: 25,
  type: 2,
  vatReason: 'Enim accusamus eligendi quidem delectus qui et.',
  documentsType: 4,
  dateType: 2,
  blocked: 13,
  idCompany: 5,
  iban: 'Quibusdam ea hic consequatur pariatur placeat et ullam qui quasi.',
  currency: 'Praesentium quae officia dolores occaecati qui.',
  address: 'Voluptas minima incidunt mollitia qui aliquam consequatur explicabo voluptatem voluptate.',
  orders: 'Impedit quibusdam eveniet in quasi fugit molestiae quos maiores.',
  ddts: 'Ullam enim itaque consequatur magni.',
};
export const currentDocumentHeader: DocumentHeaderResponse = {
  actualCompany: company,
  availableCompanies: [company],
  actualDocumentType: documentType,
  availableDocumentType: docTypeArray,
  actualHMoveType: move,
  availableHMoveTypes: [move],
  fatherProtocolIn: '1352',
  subject: subjectVal,
  readOnlyHeader: ['availableDocumentType'],
  supplierCode: '',
  triggerConfig: null,
};
export const hTableValues: HTableValues = {
  code: 'Impedit pariatur nostrum sed commodi adipisci et ab qui.',
  description: 'dolores aliquam quos',
};
export const boundingBoxVals: BoundingBox = { x1: 0, y1: 0, x2: 0, y2: 0 };
export const activeBoxVal: ActiveBox = {
  ...boundingBoxVals,
  pageNumber: 1,
};
export const suggestionsBox: AIsuggestion = {
  boundingBox: boundingBoxVals,
  confidence: 29,
  content: 'Ut ut est. Odi impedit dolores.',
  number: 40,
  pageNumber: 32,
  status: 3,
};
export const docSectionField: DocumentFieldsSectionField = {
  archive: '-`+,=ZefA1',
  column: 'MU_-fw%FY,',
  dataLink: '*!KB!UtZm]',
  defval: '}=Nee^X(Hj',
  fieldGroup: null,
  fieldInputType: 0,
  fieldName: 'Quo vel quisquam voluptatem.',
  fieldType: 3,
  formula: 'Ipsum et minus architecto vel eius.',
  hTableValues: [hTableValues],
  idField: 10,
  idFieldGroup: 1,
  inlineFields: '_*b{catt.-',
  key: 46,
  multi: 16,
  obbl: 38,
  position: 0,
  translate: 'Modi voluptate aut ipsum.',
  values: [suggestionsBox],
  triggerConfig: null,
};
export const documentFieldsection: DocumentFieldsSection = {
  protocol: 1,
  idSection: 1,
  description: 'Qui ut minus eligendi quia qui.',
  sectionFields: [docSectionField],
};
export const documentsFields: DocumentFieldsSectionField[] = [docSectionField];
export const focussedCellval: focussedCell = {
  rowId: 0,
  columnId: 0,
  type: 0,
};

export const editDocumentVal: EditDocuments = {
  balanceField: 'Qui ut minus eligendi quia qui.',
  columnsBody: null,
  nameBody: 'Qui ut minus eligendi quia qui.',
  headerName: 'Qui ut minus eligendi quia qui.',
  documentFields: [documentFieldsection],
  documentDecimal: 2,
  documentHeader: currentDocumentHeader,
  showBody: true,
  focussedQ3Cell: focussedCellval,
  fieldGroupId: 0,
  isDocReadOnly: false,
  ocr: {
    q1: {
      fieldInputType: 0,
      name: 'Qui ut minus eligendi quia qui.',
    },
  },
  predictionsData: [],
  formattedPredictionsData: [],
  selectedRowPrediction: null,
};
export const column: Column = {
  idTemplateColumn: 1,
  name: 'col1',
  position: 1,
  translate: 'translation',
  type: 'String',
  filterType: 'free',
  width: 100,
};
export const columsval: Column[] = [
  {
    idTemplateColumn: 45,
    name: 'Larissa_Legros',
    position: 41,
    width: 8,
    translate: 'Rowan.Bosco27',
    type: 'String',
    filterType: 'free',
  },
  {
    idTemplateColumn: 26,
    name: 'Eleanore.Auer93',
    position: 36,
    width: 20,
    translate: 'Drew.Abshire64',
    type: 'String',
    filterType: 'free',
  },
  {
    idTemplateColumn: 42,
    name: 'Ottis18',
    position: 30,
    width: 20,
    translate: 'Lorine_Rempel',
    type: 'String',
    filterType: 'free',
  },
  {
    idTemplateColumn: 13,
    name: 'Mckayla19',
    position: 37,
    width: 25,
    translate: 'Albert17',
    type: 'String',
    filterType: 'free',
  },
  {
    idTemplateColumn: 41,
    name: 'Benjamin_Harris',
    position: 0,
    width: 12,
    translate: 'Maybell36',
    type: 'String',
    filterType: 'free',
  },
];

export const tableColumns: TableColumn = {
  columnsList: columsval,
  fixedColumns: 0,
};

export const rows: Row[] = [
  {
    ['col1']: 'row1',
    wfType: 7,
    funzione: 23,
    company: 'Lucius82',
    codCluster: 'Boyd54',
    causale: 'Harum rerum quo mollitia et a ut dolorum vero.',
    senderType: 36,
    protocol: 1,
    isAllAuth: true,
    companyName: 'Myra_Davis',
  },
  {
    ['col1']: 'row2',
    wfType: 38,
    funzione: 38,
    company: 'Dean4',
    codCluster: 'Sigurd.Moen26',
    causale: 'Qui aut ut impedit et pariatur.',
    senderType: 50,
    protocol: 2,
    isAllAuth: false,
    companyName: 'Giuseppe.Adams',
  },
  {
    ['col1']: 'row3',
    wfType: 19,
    funzione: 28,
    company: 'Julio77',
    codCluster: 'Lonzo68',
    causale: 'Voluptatem deserunt voluptatem qui nemo vitae nemo laboriosam.',
    senderType: 42,
    protocol: 3,
    isAllAuth: true,
    companyName: 'Kane.Schaden65',
  },
  {
    ['col1']: 'row4',
    wfType: 14,
    funzione: 41,
    company: 'Gay_Klein',
    codCluster: 'Arvilla78',
    causale: 'Incidunt error ex impedit laudantium nam vel.',
    senderType: 24,
    protocol: 4,
    isAllAuth: false,
    companyName: 'Bernie.Reilly23',
  },
];
export const fieldGroupColumn: FieldGroupColumn = {
  code: 1,
  hTableValues: [
    {
      code: 'string',
      description: 'string',
      sign: 1,
    },
    {
      code: 'string1',
      description: 'string1',
    },
  ],
  title: 'string',
  translate: 'string',
  type: 1,
  dataType: 3,
  fixedName: 'string',
  link: 'string',
  position: -1,
  width: -1,
  preference: -1,
};
