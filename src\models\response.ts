/* eslint max-lines: off */
import { Program, UserData } from 'models/user';
import { ReportTemplateItem, ReportFilter } from './report';
import { IFilterValue, Q3Values } from './request';
import { FieldStatus } from './documents';
import { PrivilegeConfigureRole, ProgramConfigureRole } from './configurator';
import { Icon } from 'components/CTCard';

// Attachments
export interface Attachment {
  attach: string | null;
  dateIn: number;
  description: string | null;
  disableDate: number | null;
  fileExtension: string;
  filename: string;
  idDocAttach: number;
  idExternalDocumentary: number | null;
  mimeType: string;
  protocol: number;
  username: string;
  usernameDisable: string | null;
}

export type LookAndFeel = {
  headerText: string;
  headerColor: string;
  buildNumber: string;
  logo: string; // base64
  maintMsg: string;
  openIdUrl: string;
};

export type changePasswordResponse = {
  pwdIsChanged: boolean;
  message: string;
};

export type DocumentFields = DocumentFieldsSection[];
export interface DocumentFieldsSection {
  protocol: number;
  idSection: number;
  description: string;
  sectionFields: DocumentFieldsSectionField[];
}

/**
0 string
1 date  "DD/MM/YYYY"
2 number
3 currency "####,##"
4 boolean "true"/"false"
5 text area
*/
export type FieldInputType = 0 | 1 | 2 | 3 | 4 | 5 | 21;

/**
21 READONLY
0 MANUAL, <input />
3 LIST, <select />
7 COMBO, <select /> with manual option too
*/
export type fieldType = 0 | 1 | 3 | 7 | 21;
export interface DocumentFieldsSectionField {
  archive: any;
  column: any;
  dataLink: any;
  defval: any;
  fieldGroup: FieldGroup | null;
  fieldInputType: FieldInputType;
  fieldName: string;
  fieldType: fieldType;
  formula: string;
  hTableValues: HTableValues[];
  idField: number;
  // when not null it has a Q3 section and consequently the fieldGroup has some related information.
  idFieldGroup: number | null;
  inlineFields: any;
  key: number;
  multi: number;
  obbl: number;
  position: number;
  translate: string;
  values: AIsuggestion[];
  fixedName?: string;
  triggerConfig: FieldTriggerConfig | null;
}
export interface PredictionContent {
  content: string;
  confidence: number;
  idxNumber: number;
}
export interface PredictionColumn {
  [key: string]: {
    idColumn: number;
    rowNumber: number;
    idFieldGroup: number;
    fixedName: string;
    predictionContentList: PredictionContent[];
  };
}

export interface FieldGroup {
  description: string;
  balanceField: string | null;
  fieldColumn: FieldGroupColumn[];
  fixedName: string;
  idCompany: number;
  idDocType: number;
  idFieldGroup: number;
  name: string;
  tableValues: BodyRow[];
  predictionsValues: PredictionColumn[];
}

export interface FieldGroupColumn {
  code: number;
  hTableValues: {
    code: string;
    description: string;
    sign?: 1 | -1;
  }[];
  title: string;
  type: FieldInputType;
  dataType: fieldType;
  fixedName: string;
  link: string;
  position: number | null;
  preference: number | null;
  translate: string | null;
  width: number;
}

export interface BodyRow {
  [key: string]: {
    confidence: number;
    content: string;
    idColumn: number;
    pageNumber: number;
    rowNumber: number;
    x1: number;
    x2: number;
    y1: number;
    y2: number;
  };
}

export interface AIsuggestion {
  boundingBox: BoundingBox;
  confidence: number;
  content: string;
  number: number;
  pageNumber: number;
  status: FieldStatus;
}

export interface BoundingBox {
  x1: number;
  x2: number;
  y1: number;
  y2: number;
}

export interface Company {
  idCompany: number;
  name: string;
  label: string;
  vatNumber: string;
  socialReason: string;
  fiscalCode: string;
  phone: string;
  fax: string;
  email: string;
  url: string;
  onlineCode: string;
  referenceUser: number | undefined;
  counterPrefix: string;
  country: string;
  ocrDoc?: boolean;
  documentTypeList?: DocumentType[];
  currency: string;
  countryCurrency: string;
}

export interface DocumentType {
  idDocType: number;
  docName: string;
  fixedName: string;
  batchDistribute: number;
  docType: number;
  creditDebit: number;
  genericOption: boolean;
  ocr: boolean;
  hide: boolean;
}

export interface MovType {
  changeSign: boolean;
  code: string;
  description: string;
  idCompany: number;
  q3: boolean;
}

export interface SubjectDocHeader {
  idWSubject: number;
  subject: string;
  vatNumber: string;
  date: string;
  fiscalCode: string;
  tel: string;
  fax: string;
  email: string;
  url: string;
  status: number;
  type: number;
  vatReason: string;
  documentsType: number;
  dateType: number;
  blocked: number;
  idCompany: number;
  iban: string;
  currency: string;
  address: string;
  orders: string;
  ddts: string;
}

interface TriggerAction {
  action: 'removeField' | 'callApi';
}
export interface RemoveFieldAction extends TriggerAction {
  action: 'removeField';
  target: number;
}
export interface CallApiAction extends TriggerAction {
  action: 'callApi';
  endpoint: string;
  errorMessage: string;
  successMessage: string;
}
export type ActionType = RemoveFieldAction | CallApiAction;

/**
 * TriggerConfig for DOCUMENT HEADER
 * (Associated with a specific key, ex "supplierCode")
 */
interface DocumentHeaderTriggerConfig {
  key: string;
  actions: ActionType[];
}

/**
 *  TriggerConfig for Q1 FIELDS
 * (No key, just a list of actions)
 */
export interface FieldTriggerConfig {
  actions: ActionType[];
}
export interface DocumentHeaderResponse {
  actualCompany: Company | null;
  availableCompanies: Company[] | null;
  actualDocumentType: DocumentType | null;
  availableDocumentType: DocumentType[] | null;
  availableHMoveTypes?: MovType[] | null;
  actualHMoveType?: MovType | null;
  fatherProtocolIn: string | null;
  subject: SubjectDocHeader;
  numberOfDetails?: number;
  readOnlyHeader: string[];
  supplierCode: string | null;
  triggerConfig: DocumentHeaderTriggerConfig[] | null;
}

export interface HTableValues {
  code: string;
  description: string;
  decimals?: number;
}

export interface DocumentLockedResponse {
  protocol: number;
  locked: boolean;
  usernameLock: string | null;
  nameLock: string | null;
  programName: string | null;
  lockedAcquired: boolean;
  lockedEnabled: boolean;
}

export interface Subject {
  idWSubject: number;
  subject: string;
  vatNumber: string;
  date: string;
  fiscalCode: string;
  supplierCode: string;
  tel: string;
  fax: string;
  email: string;
  url: string;
  status: number;
  typology: number;
  vatReason: string;
  validationType: number;
  dateType: number;
  blocked: number;
  idCompany: number;
  iban: string;
  currency: string;
  address: string;
  orders: string;
  ddts: string;
  debitCredit: number;
  notes: string;
}

export interface UserCompaniesAction {
  type: string;
  active: boolean;
  actionPriv: number;
  idAction: number;
  idProgram: number;
  checkBeforeAction: boolean;
}

export interface UserCompany {
  idCompany: number;
  name: string;
  priv: number;
  actions: UserCompaniesAction[];
  socialReason: string;
}

export interface CompanyDocTypeUserResponse {
  idCompany: number;
  companyName: string;
  docType: DocumentType[];
  users: FinalDestinationUser[];
}

export type User = UserData;

export interface MenuPrograms {
  postLoginApp: number;
  programs: Program[];
}

export interface TableColumn {
  fixedColumns: number;
  columnsList: Column[];
}

export type localizationString = 'LocalizableString' | 'LocalizableStringFe' | 'date' | 'stringDate';

export interface Column {
  idTemplateColumn: number;
  name: string;
  position: number;
  width: number;
  translate: string;
  type: 'Number' | 'String' | 'Date' | 'StringDate' | localizationString;
  filterType: 'free' | 'select' | 'date' | 'none';
  minWidth?: number;
  maxWidth?: number;
}
export interface UpdateTableRowsPayload {
  protocols: (number | null)[];
  property: string;
  value: any;
}
export interface Template {
  idTemplate: number;
  description: string;
  fixedColumns: number;
  readOnly: boolean;
  archive: boolean;
  customFilter: string;
  customLabel: string;
  // eslint-disable-next-line camelcase
  actions_document_list: string[];
  // eslint-disable-next-line camelcase
  actions_document_open: string[];
  subViews: Template[];
  dynamicFilters: boolean;
  hideAllDocumentsDropdown: boolean;
  showSavedFilters: boolean;
}

export interface UsersResponse {
  [index: string]: any;
  idUser: number;
  name: string;
  password: string;
  username: string;
  email: string;
  groupDistribution: number;
  preferredDateFormat: string;
  decimalSeparator: string;
  loginType: string;
  ldapConf: number;
  blocked: number;
  loginAttempts: number;
  userDescription: string;
  lastLogin: string;
  address: string;
  clientPort: number;
  userImage: string;
  token: string;
  language: string;
  sendMail: number;
  changePassword: number;
  expirationDate: number;
}

export interface BulkInsertResponse {
  processedCount: number;
  rowsInError: {
    id: number;
    rowNumber: number;
    idCompany: number;
    subject: string;
    vatNumber: string;
    fiscalCode: string;
    errorMessage: string;
  }[];
}

export interface SupplierCodes {
  idDetail: number;
  idSubject: number;
  index: number;
  content: string;
}

export interface SubjectDetail {
  content: string;
  idDetail: number;
  idSubject: number;
  supplierCode: string;
}
export interface SearchSubject {
  idWSubject: number;
  subject: string;
  vatNumber: string;
  date: string;
  fiscalCode: string;
  tel: string;
  fax: string;
  email: string;
  url: string;
  status: number;
  type: number;
  vatReason: string;
  documentsType: number;
  dateType: number;
  blocked: number;
  idCompany: number;
  iban: string;
  currency: string;
  address: string;
  orders: string;
  ddts: string;
  supplierCodeSearch: string;
  supplierCodes: SupplierCodes[];
  supplierCode?: string;
  validationType: number;
  details: SubjectDetail[];
}

export interface FatherContracts {
  protocol: number;
  endDate: string;
  mallAddress: string;
  mallCity: string;
  mallName: string;
  mallPostalCode: string;
  mallProvince: string;
  startDate: string;
  subject: string;
}

export interface GetZoneTextResponse extends BoundingBox {
  content: string | null;
  fieldType: string;
}

export interface MetadataList {
  idMetadata: number;
  matadataKey: string;
}

export interface DocumentTypeWithMeta {
  idDocType: number;
  docName: string;
  batchDistribute: number;
  docType: number;
  creditDebit: number;
  genericOption: boolean;
  ocr: boolean;
  hide: boolean;
  metadataList: MetadataList[];
}

export interface CompanyWithDocAndMeta {
  idCompany: number;
  name: string;
  label: string;
  vatNumber: string;
  socialReason: string;
  fiscalCode: string;
  phone: string;
  fax: string;
  email: string;
  url: string;
  onlineCode: string;
  referenceUser: number;
  counterPrefix: string;
  country: string;
  documentTypes: DocumentTypeWithMeta[];
}

export interface CheckDiskCount {
  path: string;
  count: number;
}

export interface SentMail {
  mailAddress: string;
  counter: number;
}

export interface ReceivedMail {
  mailAddress: string;
  counter: number;
}

export interface StatusMail {
  company: string;
  description: string;
  docType: string;
  idCompany: number;
  idDocType: number;
  mail: string;
  sourceStatusTs: string;
  status: number;
}

export interface CheckDiskStatus {
  path: string;
  status: number;
}

export interface GetMailMonitor {
  company: string;
  documentName: string;
  documentType: string;
  dateIn: number | Date;
  protocolIn: string;
  protocol: string | number;
  bucketPath: string;
  fromAdr: string;
  toAdr: string;
  subject: string;
  position: string;
}

export interface GetMailDocumentHistory {
  company: string;
  documentName: string;
  documentType: string;
  dateIn: number | Date;
  protocolIn: string;
  protocol: number;
  bucketPath: string;
  incChannel: string;
  priority: string;
  status: string;
  senderUser: string;
  position: string;
  fromAdr: string;
  toAdr: string;
  subject: string;
}

export interface GetDiskMonitorResponse {
  company: string;
  documentName: string;
  documentType: string;
  dateIn: string;
  protocolIn: string;
  protocol: string;
  bucketPath: string;
  position: string;
}

export interface GetFileUploadMonitorResponse {
  company: string;
  documentName: string;
  documentType: string;
  dateIn: string;
  protocolIn: string;
  protocol: string;
  bucketPath: string;
  position: string;
}

export interface DocumentHistoryResponse {
  company: string;
  documentName: string;
  documentType: string;
  dateIn: string;
  protocolIn: string;
  bucketPath: string;
  incChannel: string;
  priority: string;
  status: string;
  senderUser: string;
  position: string;
}

export interface SimpleError {
  message: string;
  errorUri: string;
  exceptionThrown: string;
}

export interface Error {
  errorType: string;
  dateIn: string;
  dateTry: string;
  errorMessage: string;
  company: string;
  sourceChannel: string;
  formAdr: string;
  toAdr: string;
  subject: string;
  docName: string;
  originalProtIn: string | null;
  originalProtocol: number | null;
  protocol: number;
  documentType: string;
}

export interface Job {
  name: string;
  state: string;
  scheduleTime: string;
}

export interface GetReports {
  reports: ReportFolder[];
}

interface ReportFolder {
  idReport: number;
  folderName: string;
  type: string;
  children: ReportFile[];
}

export interface ReportFile {
  idReport: number;
  idParent: number;
  reportName: string;
  type: string;
  query: string;
}

export interface ReportExecuted {
  reportName: string;
  extension: string;
  reportSize: number;
  reportDate: string;
  reportPath: string;
}
export interface Job {
  name: string;
  state: string;
  scheduleTime: string;
}
export interface GetInfoForQuery {
  table: string;
  source: string;
  selectItems: ReportTemplateItem[];
  filters: ReportFilter[];
  csvFormat: boolean;
}

export interface ExecReport {
  file: string;
  message: string;
  reportStatus: 1;
}

interface SuccessBulkActions {
  success: true;
  protocols: null;
}

interface FailBulkActions {
  success: false;
  protocols: number[];
}

export interface SuccessScanDate {
  success: true;
  protocolsList: {
    protocol: number;
    protocolIn: string;
    changedSuccessfully: true;
  }[];
}

export interface FailScanDate {
  success: false;
  protocolsList: {
    protocol: number;
    protocolIn: string;
    changedSuccessfully: boolean;
  }[];
}

/**
 * A common type of response for all APIs acceppting an array of protocols as argument
 */
export type BulkActions = SuccessBulkActions | FailBulkActions;

export interface SelfValidate {
  protocols: number[];
  idUser: number;
  idUserTo: number;
  idProgram: number;
}

interface WfCausalReject {
  causalDesc: string;
  causalName: string;
  idCompany: number;
  idWfCausal: number;
}

export interface HistoryRecord {
  causalCode: string;
  note: string;
  protocol: number;
  rejectionReason: string;
  sendDate: number;
  type: number;
  typeDescription: string;
}
export interface OpenReject {
  idUser: number;
  name: string;
  noteMandatory: boolean;
  wfCausalRejects: WfCausalReject[];
  wfHistory: HistoryRecord[];
}
// #region Wfhistory response type
export interface OpenPark {
  noteMandatory: boolean;
  wfHistory: HistoryRecord[];
}

export interface ApproveWfTaskReponse {
  protocol: number;
  response: string;
  responseStatus: string;
}

// #endregion
export interface ApplyRulesResponse {
  docStatus: 1 | 2 | 3;
  docMessageStatus: string | null;
  fields: {
    idField: number;
    fieldValues: string[];
    accountFields: Q3Values | null;
    num: number;
    fieldStatus: 0 | 1 | 2 | 3;
    messageField?: string | null;
  }[];
}
export interface WfInfoResponse {
  causals: Causal[];
  definitions: Definition[];
  causalNotes: {
    [key: string]: {
      idNote: number;
      idCompany: number;
      casualName: string;
      note: string;
    }[];
  };
}

export interface Causal {
  idWfCausal: number;
  causalName: string;
  causalDesc: string;
  weight: number;
  idCompany: number;
}

export interface Definition {
  idDefinition: number;
  wfName: string;
  wfDesc: string;
  idCompany?: number;
  suspendType?: number;
  noteMandatory?: boolean;
  defMaxWeight?: number;
  tasks: Task[];
}

export interface Activities {
  idActivity: number;
  orderActivity: number;
  idTaskDefinition: number;
  toPosition: number;
  mandatory: boolean;
  activity: any;
}
export interface Task {
  idTask: number;
  idTaskDefinition?: number;
  idDefinition?: number;
  taskName: string;
  taskDesc: string;
  position?: number;
  nextPosition?: number;
  escalation?: boolean;
  taskWeight?: number;
  taskAction?: {
    idAction?: number;
    name?: string;
    description?: string;
  };
  groups: Group[];
  activities?: Activities[];
}

export interface GroupTask {
  idGroup: number;
  idTaskDefinition: number;
  groupRelationType: number;
  amountLimit: number;
}

export interface Group {
  amountLimit?: number;
  idGroup: number;
  groupName: string;
  groupDesc: string;
  users: GroupUser[];
  groupUsers?: GroupUser[];
  relation?: number;
  groupTasks?: GroupTask;
}

export interface GroupUser {
  idUser: number;
  name: string;
  username: string;
  idTaksDefinition?: number;
  email?: string | null;
  timeoutSendMail?: number;
  startDeltaReminder?: number;
  endDeltaReminder?: number;
  reminderDays?: number;
  maxReminder?: number;
}

export interface OpenConvalidaResponse {
  referenceNextTask: null | {
    referenceNextTask: Task[];
    lastTask: boolean;
  };
  reference: null | {
    nextTasks: Task[];
    lastTask?: boolean;
    definitions: Definition[];
  };
  escalationGroups: null | {
    groups: Group[];
  };
  definitionClosable: boolean;
  finalDestinationUser: null | FinalDestinationUser;
  defaultNoteLog: string;
}

export interface FinalDestinationUser {
  idUser: number;
  name: string;
  username: string;
  email: string;
}

export interface GroupForInfo {
  idWorkflowDefinition: number;
  wfName: string;
  idCompany: number;
  groups: Group[];
}

export interface OpenRejectToPrevious {
  wfHistory: HistoryRecord[];
  noteMandatory: boolean;
  previousTasks: Task[];
}

export interface OpenInfoFromWorkflow {
  wfMovements: HistoryRecord[];
  getGroupsForInfo: GroupForInfo[];
}

export interface OpenConvalidaInfoResponse {
  groups: Group[];
  finalDestinationUser: null | FinalDestinationUser;
  wfMovements: HistoryRecord[];
}

export interface LitigationCausal {
  idCausal: number;
  causalName: string;
  causalDesc: string;
  idCompany: number;
  defaultCausal: boolean;
  templateEditable: boolean;
  attachmentMail: boolean;
}

export interface LitigationCausalResponse {
  causalLitigationList: LitigationCausal[];
  mailSupplier: string | null;
}

export interface GetWfHistoryResponse extends HistoryRecord {
  sender: UsersResponse;
  wfDefinition: Definition;
  recipient: Group[];
}

export interface OpenCloseLitigationResponse {
  wfHistory: HistoryRecord[];
}

export interface GetEngineInfoResponse {
  clientPort: number;
  urlToCall: string;
}

export interface PendingResponse {
  protocol: number;
  fileName: string;
  idCompany: number;
  companyName: string;
  idDocType: number;
  documentTypeName: string;
  dateIn: string;
  acquisitionDate: string;
  incChannel: number;
  sourceChannel: string;
  dateTry: string;
}
export interface ParseTransactionResponse {
  validationCheckPassed: boolean;
  validationCheckReturnValues: ValidationCheckReturnValues[];
  data: Data;
}

export interface ValidationCheckReturnValues {
  passed: boolean;
  message: string;
  expression: string;
}

export interface Data {
  function: string;
  records: Record[];
}

export interface Record {
  programId: string;
  transact: string;
  program: string;
  dynpro: string;
  dynbegin: string;
  fnam: string;
  value: string;
}

export interface SapIntegrationTransactionExecuteResponse {
  description?: string;
  kind?: string;
  message?: string;
  name?: string;
  next?: string;
  sapTemplateName?: string;
  sapTransactionName?: string;
  sapTransactionType?: string;
  openWorkflowRequest?: OpenWfDetails;
  blocking?: boolean;
  messages?: string[];
  showPopup?: boolean;
}

export interface OpenWfDetails {
  idCausal: number | null;
  idGroups: number[] | null;
  idTaskDefinition: number | null;
  idWorkflowDefinition: number | null;
  idCompany: number | null;
  note: string | null;
}

export interface TableValue {
  table: string;
  tableDesc: string;
  editable: boolean;
  addDisabled: boolean;
}
export interface DeleteUserResponse {
  deleteRoleStatus: string;
  deleteCodeStatus: number;
  message: string;
}

export interface ErrorResponse {
  statusCode: number;
  status: string;
  statusMessage: string;
  actionsToDo: null;
}
export interface TablesDataResponse {
  columns: TablesDataColumns[];
  queryResults: any[];
  checkCompanyPriv: boolean;
}

export interface TablesDataColumns {
  columnName: string;
  columnHeader: string;
  keyField: boolean;
  required: boolean;
  position: number;
  readOnly: boolean;
  hidden: boolean;
  type: string;
  size: number;
  property?: string;
}
export interface GetTemplatePdfResponse {
  idTemplate: number;
  pdfBase64: string;
  jsonBase64: string;
}

export interface AuthorizedSplitAndFixDocumentsResponse {
  protocol: number;
  protocolIn: string;
  acquisitionDate: string;
  idCompany: number;
  idSubject: number;
}

export interface SubjectDetailColumns {
  position: number;
  title: string;
  width: number;
  q1FixedName: string;
  idCompanyDetail: number;
  readOnly: boolean;
  link: string;
  type: number;
  allowedValues: any[] | null;
}
export interface SubjectDetailRows {
  [key: number]: {
    [key: number]: {
      content: string;
      idDetail: number;
      isSubject: number;
      supplierCode: string;
      position: number;
    };
  };
}

// Roles
export interface GetUserCompanyRoles {
  idCompany: number;
  name: string;
  userRoleResponsesList: UserRoleResponsesList[];
}

export interface UserRoleResponsesList {
  idUser: number;
  username: string;
  name: string;
  email: string;
  roleProgramResponseList: RoleProgramResponseList[];
}

export interface RoleProgramResponseList {
  idRole: number;
  roleName: string;
  programResponseList: ProgramResponseList[];
}

export interface ProgramResponseList {
  idProgram: number;
  programName: string;
  idPrivilege: number;
  description: string;
}

export interface GetUserForCompany {
  idUser: number;
  username: string;
  name: string;
  email: string;
  assigned: boolean;
  disabled?: boolean;
  status?: number;
}

export interface GetUserCloneRole {
  usernameRole: string;
  nameRole: string;
  emailRole: string;
  idUser: number;
  status?: number;
}

// Configure roles

interface NewRoles {
  availablePrograms: ProgramConfigureRole[];
  availablePrivileges: PrivilegeConfigureRole[];
}

export interface EditRoles {
  idRole: number;
  roleName: string;
  roleProgramPrivilegeResponseList: ProgramConfigureRole[];
}

export interface GetConfigureRoles {
  newRoles: NewRoles;
  editRoles: EditRoles[];
}

export interface ConfiguredRole {
  idRole: number;
  roleName: string;
  roleProgramPrivilegeResponseList: ProgramConfigureRole[];
}

export interface MicroCategoryList {
  idCompany: number;
  code: string;
  description: string;
  language: string;
}
export interface WSubjectsList {
  idWSubject: number;
  subject: string;
  vatNumber: string;
  date: string;
  fiscalCode: string;
  supplierCode: string | number;
  tel: string;
  fax: string;
  email: string;
  url: string;
  status: number;
  typology: number;
  vatReason: string;
  validationType: string;
  dateType: number;
  blocked: number;
  idCompany: number;
  iban: string;
  currency: string;
  address: string;
  orders: string;
  ddts: string;
  debitCredit: number;
  notes: string;
}

export interface CompanyActionTable {
  idCompany: number;
  companyName: string;
  microCategoryList: MicroCategoryList[] | null;
  wSubjectsList: WSubjectsList[] | null;
}

export interface SubjectList {
  supplierCode: string;
  supplierName: string;
  latency: number;
}

export interface MicroCategoryLatency {
  microcategoryCode: string;
  microcategoryDescription: string;
  latency: number;
}

export interface PoDefaultRow {
  id?: string;
  idAction: number;
  idCompany: number;
  companyName: string;
  fullOcr: boolean;
  fluxName: string;
  wfCausal: string;
  wfIdCausal: number;
  wfIdWorkflowDefinition: number;
  workflowDefinition: string;
  [key: string]: any;
}
export interface PoMicroCategoryList extends PoDefaultRow {
  microcategoryCode: string;
  microcategoryDescription: string;
  subjectList: SubjectList[];
}

export interface PoVendorList extends PoDefaultRow {
  supplierCode: string;
  subjectName: string;
  microcategoryLatency: MicroCategoryLatency[];
}

export type ReverseRepostReturnType = {
  reverseDocument: {
    [key: string]: any;
  };
  repostDocument: {
    [key: string]: any;
  };
  archivedDocument: {
    [key: string]: any;
  };
};

export interface SubjectList {
  supplierCode: string;
  subjectName: string;
}

export interface CorporateApproval {
  id?: string;
  corporateApproval: boolean;
  microcategoryCode: string;
  microcategoryDescription: string;
  vendorDetails: SubjectList[];
}

export interface AP2Template {
  id?: number | undefined;
  newRow?: boolean;
  idAp2Template: number;
  companyName: string;
  vendorCode: string;
  sign: string;
  taxPercent: string;
  taxCode: string;
  glAccount: string;
  costCenter: string;
  wbs: string;
  profitCenter: string;
  internalOrder: string;
  site: string;
  productLine: string;
  tradingPartner: string;
  text: string;
}

export interface AP2HTables {
  idAp2Template?: number;
  costCenter: string;
  sign: string;
  glAccount: string;
  taxCode: string;
}

export interface RolesByProgram {
  idRole: number;
  roleName: string;
  idCompany: number[];
}

export interface NopoDefaultRow {
  id?: string;
  idAction: number;
  idCompany: number;
  companyName: string;
  wfIdCausal: number;
  wfCausal: string;
  wfIdWorkflowDefinition: number;
  workflowDefinition: string;
  fluxName: string;
  ap2Template: boolean;
}
export interface GetNopoVendorResponse extends NopoDefaultRow {
  supplierCode: string;
  subjectName: string; // subject
  vendorDetails: {
    microcategoryCode: string;
    microcategoryDescription: string;
    latency: number;
  }[];
}
export type VendorNoPoList = GetNopoVendorResponse;
export interface GetNopoMicrocategoriesResponse extends NopoDefaultRow {
  microcategoryCode: string;
  microcategoryDescription: string;
  microcategoryDetails: {
    supplierCode: string;
    supplierName: string;
    latency: number;
    supplierInExpActionTable: boolean;
  }[];
}

// WORKFLOW
export interface CompanyWorkflow {
  idCompany: number;
  name: string;
}

export interface WorkflowOUserList {
  email: string;
  idUser: number;
  name: string;
  username: string;
}

export interface WorkflowGroupList {
  idGroup: number;
  groupName: string;
  oUserList: WorkflowOUserList[];
  companyName?: string;
}

export interface WorkflowTaskList {
  idTask: number;
  idTaskDefinition: number;
  taskName: string;
  taskGroups: number;
  groupList: WorkflowGroupList[];
}

export interface WorkflowCompaniesDefinitionList {
  idDefinition: number;
  companyName: string;
  wfName: string;
  suspendType: number | null;
  noteMandatory: boolean;
  currencyExchange: boolean;
  takeNextSingleTask: boolean;
  taskOnMinCausalWeight: boolean;
  taskList: WorkflowTaskList[];
}

export interface WorkflowCompaniesDefinitionListExtended extends WorkflowCompaniesDefinitionList {
  taskList: WorkflowTaskList[];
  idCompany?: number;
  subRows?: any[];
}

export interface WorkflowCompaniesDefinition {
  companyName: string;
  idCompany: number;
  companyDefinitions: WorkflowCompaniesDefinitionListExtended[];
}

export interface GetCompanyWithTasks {
  idTask: number;
  name: string;
  desc: string;
}

export interface OUserList {
  idUser: number;
  name: string;
  username: string;
  email: string;
  assignedUser: boolean;
}

export interface CompanyGroups {
  idGroup: number;
  groupName: string;
  groupDesc: string;
}

export interface CompanyGroupsUser extends CompanyGroups {
  oUserList: OUserList[];
}

export interface NewWorkflowDefinition {
  wfName: string;
  idCompany: number | null;
  suspendType: number;
  noteMandatory: boolean;
  currencyExchange: boolean;
  takeNextSingleTask: boolean;
  taskOnMinCausalWeight: boolean;
}

export interface EditWorkflowDefinition extends NewWorkflowDefinition {
  idDefinition: number;
}

export interface TaskGroupAssociations {
  idGroup: number;
  groupName: string;
  relation: number;
  amountLimit: number;
  assigned: boolean;
  currency: string;
  oUserList?: OUserList[];
}

export interface DefinitionTaskAssociations {
  idTask: number;
  taskName: string;
  escalation: boolean;
  weight: number;
  idConvalidationAction: number;
  taskGroupAssociations: TaskGroupAssociations[];
  idTaskDefinition?: number | null;
  position?: number | null;
  nextPosition?: number | null;
  convalidationDescription?: string;
}

export interface WorkflowDefinitionTaskGroupsAssociations {
  idDefinition: number;
  definitionName: string;
  definitionTaskAssociations: DefinitionTaskAssociations[];
}

export interface WorkflowDefinitionTaskGroupsAssociationsExtended extends WorkflowDefinitionTaskGroupsAssociations {
  id: number;
  subRows: DefinitionTaskAssociations[];
  idTask?: number;
  taskName?: string;
  escalation?: boolean;
  weight?: number;
  idConvalidationAction?: number;
  taskGroupAssociations?: TaskGroupAssociations[];
  idTaskDefinition?: number | null;
  position?: number | null;
  nextPosition?: number | null;
  convalidationDescription?: string;
  isSubRow?: boolean;
}

export interface NewCompanyGroups {
  idCompany: number | undefined;
  groupName: string;
  groupDesc: string;
  oUserList: OUserList[];
}

export interface CompanyDefinitionsForAssociation {
  idDefinition: number;
  wfName: string;
  wfDesc: string;
  idCompany: number;
  suspendType: number;
  noteMandatory: boolean;
  currencyExchange: boolean;
  takeNextSingleTask: boolean;
  taskOnMinCausalWeight: boolean;
}

export interface CompanyTasksForAssociation {
  idTask: number;
  name: string;
  desc: string;
}

export interface CompanyDefinitionAssociation {
  idDefinition: number;
  definitionName: string;
  subRows: DefinitionTaskAssociations[];
}

export interface CompanyGroupsForTaskAssociation {
  idGroup: number;
  groupName: string;
  relation: number;
  amountLimit: number;
  assigned?: boolean;
  currency: string;
  oUserList: OUserList[];
}

export interface CreateAssociation {
  idDefinition: number;
  definitionName: string;
  definitionTaskAssociations: DefinitionTaskAssociations[];
}

export interface WfListOfConvalidationAssociation {
  idAction: number;
  name: string;
  description: string;
}

export interface CompanyMovType {
  code: string;
  description: string;
  idCompany: number;
}

export interface CompanyCountryParams {
  country: string;
  description: string;
}

export interface CompanyFromGeoList {
  idCompany: number;
  name: string;
  socialReason: string;
}
export interface GeoCompany {
  idGbs: number;
  gbsName: string;
  companyList: CompanyFromGeoList[];
}

export interface Kpi {
  idMetric: number;
  description?: string;
  lowerRange?: number;
  higherRange?: number;
  result: number | string | null;
  semaphore?: string;
  formatType: string;
  name: string;
  timeframePeriod: string | null;
  tooltip?: string;
  idCharts?: number[];
  idReport?: number | null;
  childMetrics?: Kpi[] | null;
}

export interface SummaryCategory {
  idCategory: number;
  name: string;
  description: string;
  kpis: Kpi[];
  icon: Icon;
}

export interface Area {
  idArea: number;
  name: string;
  kpis: Kpi[];
  average: {
    result: number;
  };
  showSummaryDetails: boolean;
}

export interface DetailCategory {
  idCategory: number;
  name: string;
  areas: Area[];
  unifyHeaders: boolean;
}

export interface ChartData {
  id: number;
  name: string;
  label: string;
  description: string;
  chartType: string;
  series: Series[];
}

export interface Series {
  name: string;
  label: string;
  description: string;
  data: Value[];
  average: boolean;
  axisFormatTypes: {
    x: string;
    y: string;
  };
}

export interface Value {
  x: string;
  y: number | string;
}
export interface SuppliersIbanList {
  bankl: string;
  bankn: string;
  bvtyp: string;
  iban: string;
  sapFamily: string;
  supplierCode: string;
}

export interface IOption {
  label: string;
  value: string | number | (string | number)[];
}

export interface IOptions {
  [userId: string]: {
    [dependentValue: string]: IOption[];
  };
}

export interface VisibleCondition {
  dependentOn: number;
  value: string[];
}

export interface InputPropDinamicFilter {
  options?: IOptions | { [key: string]: IOption[] };
  dependentFilters?: number[];
  dependentOn?: number[] | null;
  enableDateRange?: boolean;
  rangeFilterName?: { from: string; to: string };
  isInputCombined?: boolean;
  inputCombinedOptions?: InputCombinedOptions[];
  filterNameDependent?: string;
  translate?: boolean;
  defaultOptions?: IOption[];
  visibleCondition?: VisibleCondition;
}

export interface ValidationOfInputCombined {
  field: string;
  is: number;
  otherwise: {
    message: string;
    regex: string;
  };
  then: {
    message: string;
    regex: string;
  };
}

export interface ValidationOfDinamicFilter {
  type: string;
  required?: boolean;
  message?: string;
  when: ValidationOfInputCombined;
  validationDependent?: ValidationOfDinamicFilter;
}

export interface InputCombinedOptions {
  filterName: string;
  label: string;
  value: number;
  inputType?: string;
  isTextArea?: boolean;
  isRange?: boolean;
}

export interface IFilterDinamicForm {
  idFilter: number;
  label: string;
  filterName: string[];
  inputType: string;
  inputProp?: InputPropDinamicFilter;
  validation?: ValidationOfDinamicFilter | null;
}
export interface IExtractDocsResponse {
  [protocol: string]: string;
}

export interface IOpenWorkflowNotifications {
  idUser: number;
  name: string;
  openWfSendMail: boolean;
  openWfSendNotificationImmediately: boolean;
  openWfMaxDocuments: number;
  openWfMaxDocumentDetails: number;
  idTemplateImmediateOpenWorkflow: number;
  idTemplateScheduledOpenWorkflow: number;
  openWfFrequency: string;
}

export interface IWfReminderNotifications {
  idUser: number;
  name: string;
  reminderSendMail: boolean;
  startDeltaReminder: number;
  endDeltaReminder: number;
  reminderDays: number;
  maxReminder: number;
  reminderMaxDocuments: number;
  reminderMaxDocumentDetails: number;
  idTemplateScheduledReminder: number;
  reminderFrequency: string;
}
export interface IOverdueNotifications {
  idUser: number;
  name: string;
  overDueSendMail: boolean;
  overDueMaxDocuments: number;
  idTemplateScheduledOverdue: number;
  overDueFrequency: string;
}

export interface WfCausalNameReject {
  causalName: string;
  causalDesc: string;
}
export interface IOpenWorkflowNotifications {
  idUser: number;
  name: string;
  openWfSendMail: boolean;
  openWfSendNotificationImmediately: boolean;
  openWfMaxDocuments: number;
  openWfMaxDocumentDetails: number;
  idTemplateImmediateOpenWorkflow: number;
  idTemplateScheduledOpenWorkflow: number;
  openWfFrequency: string;
}
export interface IWfReminderNotifications {
  idUser: number;
  name: string;
  reminderSendMail: boolean;
  startDeltaReminder: number;
  endDeltaReminder: number;
  reminderDays: number;
  maxReminder: number;
  reminderMaxDocuments: number;
  reminderMaxDocumentDetails: number;
  idTemplateScheduledReminder: number;
  reminderFrequency: string;
}
export interface IOverdueNotifications {
  idUser: number;
  name: string;
  overDueSendMail: boolean;
  overDueMaxDocuments: number;
  idTemplateScheduledOverdue: number;
  overDueFrequency: string;
}
export interface GetTemplatesMailNotification {
  idMailTemplate: number;
  templateName: string;
}
export interface ITemplateSavedFilters {
  idSavedFilter: number;
  idUser: number;
  idProgram: number;
  idTemplate: number;
  description: string;
  value: {
    filters: IFilterValue[];
  };
}
