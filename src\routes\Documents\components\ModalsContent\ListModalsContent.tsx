import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { modalActionType, actionType } from 'utils/constants';

import Attachments from './modals/Attachments';
import BackModal from './modals/BackModal';
import Columns from './modals/Columns';
import DeleteDocuments from './modals/DeleteDocuments';
import Layout from './modals/Layout';
import Log from './modals/Log';
import Mail from './modals/Mail';
import Memo from './modals/Memo';
import NewStore from './modals/NewStore';
import NoSubjectDetail from './modals/NoSubjectDetail';
import Reassign from './modals/Reassign';
import ReassignPriority from './modals/ReassignPriority';
import SaveModal from './modals/SaveModal';
import SetScanDate from './modals/SetScanDate';
import SplitAndFix from './modals/SplitAndFix';
import RebatchDocument from './modals/RebatchDocument';
import Hotkeys from './modals/HotKeys';
import Validate from './modals/Validate';
import CompanyDocType from './modals/CompanyDocType';
import SelfValidate from './modals/SelfValidate';
import Reject from './modals/Reject';
import RejectPrevious from './modals/RejectPrevious';
import ParkDocument from './modals/ParkDocument';
import SendToWorkflow from './modals/SendToWorkflow';
import WorkflowLog from './modals/WorkflowLog';
import CloseParkDocument from './modals/CloseParkDocument';
import Confirm from './modals/Convalida';
import Selection from './modals/Selection';
import InfoFromWf from './modals/InfoFromWorkflow';
import ConvalidateInfo from './modals/Info';
import Litigation from './modals/Litigation';
import CloseLitigation from './modals/CloseLitigation';
import GoAheadModal from './modals/GoAheadModal';
import SearchSubject from './modals/SearchSubject';
import ReverseRepost from './modals/ReverseRepost';
import DocumentHistory from './modals/DocumentHistory';
import DefaultModal from 'components/Modal/DefaultModal';
import MultipleConvalida from './modals/MultipleConvalida';
import ErrorMultiConvalidation from './modals/MultipleConvalida/ErrorMultiConvalidation';
import MultipleReject from './modals/MultipleReject';
import MassiveDocTypeChange from './modals/MassiveDocTypeChange';
import CreateOrUpdateTemplateFilter from './modals/CreateOrUpdateTemplateFilter';
import DeleteConfirmationModal from './modals/DeleteConfirmation';

const ModalsContent = () => {
  const action = useSelector(selectors.modal.getActionType);

  switch (action) {
    case modalActionType.documents.CHANGE_LAYOUT:
      return <Layout />;
    case modalActionType.documents.EDIT_COLUMNS:
      return <Columns />;
    case modalActionType.documents.ADD_MEMO:
      return <Memo />;
    case modalActionType.documents.DELETE_DOCUMENTS:
      return <DeleteDocuments />;
    case modalActionType.documents.SAVE:
      return <SaveModal />;
    case modalActionType.documents.GO_BACK:
      return <BackModal />;
    case modalActionType.documents.REASSIGN_USER:
      return <Reassign />;
    case modalActionType.documents.SEND_MAIL:
      return <Mail />;
    case modalActionType.documents.ALLEGA:
      return <Attachments />;
    case modalActionType.documents.NO_SUBJECT_DETAIL:
      return <NoSubjectDetail />;
    case modalActionType.documents.REASSIGN_PRIORITY:
      return <ReassignPriority />;
    case modalActionType.documents.LOG:
      return <Log />;
    case modalActionType.documents.NEW_STORE:
      return <NewStore />;
    case actionType.SET_RECEIPT_DATE:
      return <SetScanDate />;
    case actionType.SPLIT_FIX:
      return <SplitAndFix />;
    case actionType.RETURN_TO_BATCH:
      return <RebatchDocument />;
    case modalActionType.documents.HOT_KEYS:
      return <Hotkeys />;
    case actionType.VALIDATE:
      return <Validate />;
    case modalActionType.documents.COMPANY_DOC_TYPE:
      return <CompanyDocType />;
    case modalActionType.documents.SELF_VALIDATE:
      return <SelfValidate />;
    case actionType.REJECT:
      return <Reject />;
    case actionType.MULTIPLE_REJECT:
      return <MultipleReject />;
    case actionType.REJECT_PREVIOUS:
      return <RejectPrevious />;
    case actionType.PARK:
      return <ParkDocument />;
    case actionType.CLOSEPARK:
      return <CloseParkDocument />;
    case modalActionType.documents.SEND_TO_WORKFLOW:
      return <SendToWorkflow />;
    case modalActionType.documents.WORKFLOW_LOG:
      return <WorkflowLog />;
    case modalActionType.documents.CONFIRM:
      return <Confirm />;
    case modalActionType.documents.SELECTION:
      return <Selection />;
    case actionType.ASK_INFO:
      return <InfoFromWf />;
    case modalActionType.documents.RESPONSE_INFO:
      return <ConvalidateInfo />;
    case modalActionType.documents.LITIGATION:
      return <Litigation />;
    case modalActionType.documents.CLOSE_LITIGATION:
      return <CloseLitigation />;
    case modalActionType.documents.GO_AHEAD:
      return <GoAheadModal />;
    case modalActionType.documents.SEARCH_SUBJECT:
      return <SearchSubject />;
    case modalActionType.documents.REVERSE_REPOST:
      return <ReverseRepost />;
    case modalActionType.documents.DOCUMENT_HISTORY:
      return <DocumentHistory />;
    case modalActionType.documents.FILE_IN_ACQUISITION:
      return <DefaultModal />;
    case modalActionType.documents.MULTIPLE_CONFIRM:
      return <MultipleConvalida />;
    case modalActionType.documents.MULTIPLE_CONFIRM_ERRORS:
      return <ErrorMultiConvalidation />;
    case modalActionType.documents.DOWNLOAD_ARCHIVE_PDF:
      return <DefaultModal />;
    case actionType.MASSIVE_DOC_TYPE_CHANGE:
      return <MassiveDocTypeChange />;
    case modalActionType.documents.CREATE_UPDATE_TEMPLATE_FILTER:
      return <CreateOrUpdateTemplateFilter />;
    case modalActionType.documents.DELETE_PREFERENCE_FILTER:
      return <DeleteConfirmationModal />;
    default:
      return null;
  }
};

export default ModalsContent;
