import React, { useRef } from 'react';
import { Pie } from 'react-chartjs-2';
import { colorPalette } from 'utils/styleConstants';

type Value = { x: string; y: number | string; percentage: number };

export interface PieChartProps {
  data: Value[];
  colors?: string[];
}

const PieChart = ({ data: dataValues, colors }: PieChartProps) => {
  const chartRef = useRef<any>(null);

  const data = {
    labels: dataValues.map((item) => item.x),
    datasets: [
      {
        data: dataValues.map((item) => item.y),
        percentages: dataValues.map((item) => item.percentage),
        backgroundColor: colors,
        borderColor: 'white',
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    barThickness: 17,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: {
      topLeft: 4,
      topRight: 4,
    },
    plugins: {
      legend: {
        align: 'end',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 15,
          font: {
            size: 13,
            family: 'Roboto',
            weight: 'lighter',
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: (tooltipItem: { label: any }[]) => tooltipItem[0].label,
          label: (tooltipItem: { dataIndex: number; dataset: { data: number[]; percentages: number[] } }) => {
            const value = tooltipItem.dataset.data[tooltipItem.dataIndex];
            const percentage = tooltipItem.dataset.percentages[tooltipItem.dataIndex];
            return `${value} (${percentage}%)`;
          },
        },
        backgroundColor: colorPalette.white,
        titleColor: colorPalette.turquoise.dark,
        bodyColor: colorPalette.turquoise.dark,
        displayColors: false,
        titleFont: {
          weight: 400,
        },
        bodyFont: {
          size: 12,
          weight: 400,
        },
        caretPadding: 10,
        borderColor: colorPalette.grey.grey3,
        borderWidth: 1,
        padding: 7,
      },
    },
  };

  return (
    // @ts-ignore
    <Pie ref={chartRef} data={data} options={options} />
  );
};

export default PieChart;
