import React, { useEffect, useMemo } from 'react';
import { Layout } from 'models/documents';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync, useWindowSize } from 'react-use';

import { actionType } from 'utils/constants';
import { RootState } from 'models';

import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import services from 'services';

import Routes from './routes';
import addHotkeys from 'components/HOC/decorateWithShortcut';
import { hotActions } from './hotActions';
import _ from 'lodash';
import { useHistory, useLocation } from 'react-router-dom';

interface Props {
  idProgram: number;
}

const Documents = (props: Props) => {
  const { idProgram } = props;
  const initialLayout = window.localStorage.getItem(`documents-${idProgram}-layout`) || 'rows';
  const initialPageSize = window.localStorage.getItem(`documents-${idProgram}-pageSize`);

  const allAuthorized = useSelector(selectors.documents.selectIsAllAuth);
  const isArchived = useSelector(selectors.documents.selectIsTemplateArchived);
  const idUser = useSelector(selectors.user.getIdUser);
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);
  const { val: PRIORITY_WARNING } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.PRIORITY_WARNING),
  );
  const history = useHistory();
  const location = useLocation();
  const { val: SHOW_FILTER_FOR_DOCUMENTS_LIST } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST, 0),
  );
  // const SHOW_FILTER_FOR_DOCUMENTS_LIST = showFilterConfig?.val;
  const { val: SET_ALL_DOCS_AS_DEFAULT } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.SET_ALL_DOCS_AS_DEFAULT),
  );
  const currentLayout = useSelector(selectors.documents.selectLayout);
  const companies = useSelector(selectors.app.getCompanies);
  const templates = useSelector(selectors.documents.selectTemplates);
  const dynamicFiltersByTemplate = templates.find((template) => template.idTemplate === idTemplate)?.dynamicFilters;
  const showFilter =
    dynamicFiltersByTemplate ||
    (SHOW_FILTER_FOR_DOCUMENTS_LIST && utils.user.isActionByCompanyActive(actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST));
  const config = useSelector((store: RootState) => store.app.config);
  const isSearchVisible = useSelector(selectors.documents.selectIsSearchFilterVisible);

  const isFromDrillDown =
    window.history.state && window.history.state.state && window.history.state.state.drillDownData;
  const columnsTemplatesReady = React.useRef(false);
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const { width: screenWidth } = useWindowSize();

  useEffect(() => {
    if (screenWidth <= 1240 && currentLayout === 'columns') {
      dispatch(actions.documents.setLayout('rows'));
      window.localStorage.setItem(`documents-${idProgram}-layout`, 'rows');
      dispatch(actions.modal.closeModal());
      utils.app.notify('success', t('layout-changed'));
    }
  }, [screenWidth, t, currentLayout, dispatch, idProgram]);

  // getting templates and setting first as default
  useAsync(async () => {
    try {
      if (!isFromDrillDown) {
        const { data: templates } = await services.getTemplates(idProgram);
        dispatch(actions.documents.setTemplates(templates));
        if (templates?.length) {
          const { idTemplate } = templates[0];
          dispatch(actions.documents.setActiveTemplateId(idTemplate));
          columnsTemplatesReady.current = true;
        }
        if (SET_ALL_DOCS_AS_DEFAULT === 1) dispatch(actions.documents.setIsAllAuth(true));
        return;
      }
      const { data: templates } = await services.getTemplates(idProgram);
      dispatch(actions.documents.setTemplates(templates));
      columnsTemplatesReady.current = true;
    } catch (e) {
      console.error(e);
    }
  }, []);

  useEffect(() => {
    // table page size
    dispatch(actions.documents.setTablePageSize(initialPageSize ? parseInt(initialPageSize) : 10));
    // layout
    dispatch(actions.documents.setLayout(initialLayout as Layout));
  }, [idProgram, dispatch, initialLayout, initialPageSize]);

  const checkHighPriorityDocs = (rows: any) => {
    dispatch(actions.documents.setDocumentImage(''));
    const alreadyVisited = sessionStorage[`alreadyVisited${idTemplate}`];
    const priorityCondition = () => {
      return (
        rows.findIndex((row: any) => {
          return row.priority === 2;
        }) !== -1
      );
    };
    if (PRIORITY_WARNING && priorityCondition() && !alreadyVisited) {
      utils.app.notify('warning', t('highPriorityDocuments'), 5000);
      if (!alreadyVisited) sessionStorage[`alreadyVisited${idTemplate}`] = true;
    }
  };

  // documents table's
  useAsync(async () => {
    if (!columnsTemplatesReady.current || !idTemplate || !idProgram || !idUser || !companies.length) {
      dispatch(actions.documents.setDocumentGridRows([]));
      return;
    }

    // Only change search filter visibility if not coming from drilldown
    if (isFromDrillDown) {
      history.replace({
        pathname: location.pathname,
        state: undefined,
      });
      return;
    }

    if (!_.isEmpty(config)) {
      // Get and set columns
      const {
        data: { columnsList, fixedColumns },
      } = await services.getDocumentGridColumns(idUser, idProgram, idTemplate);
      dispatch(actions.documents.setDocumentGridColumns(columnsList.map((column) => ({ ...column, minWidth: 80 }))));
      dispatch(actions.documents.setDocumentGridFixedColumns(fixedColumns));
      dispatch(actions.documents.setDocumentGridRows([]));
      dispatch(actions.documents.setResetDocumentFilterForm());
      dispatch(actions.documents.setSelectedTemplateFilter(null));

      // Early return if the document list is archived
      if (isArchived) {
        dispatch(actions.documents.setIsSearchFilterVisible(true));
        return;
      }

      if (showFilter) {
        dispatch(actions.documents.setIsSearchFilterVisible(true));
        return;
      }

      dispatch(actions.documents.setIsSearchFilterVisible(false));

      // Get and set data rows for non-archived documents
      const customFilter = templates.find((template) => template.idTemplate === idTemplate)?.customFilter;
      const { data: rows } = await services.getDocuments(
        { idProgram, idUser, idTemplate, allAuthorized },
        customFilter,
      );
      dispatch(actions.documents.setDocumentGridRows(rows));
      checkHighPriorityDocs(rows);
    }
  }, [idProgram, idTemplate, columnsTemplatesReady.current, companies, config]);

  // documents table's rows allAuthorized
  useAsync(async () => {
    // Check if initial conditions are met before proceeding
    if (!columnsTemplatesReady.current || !idTemplate || !idProgram || !idUser || isSearchVisible) {
      dispatch(actions.documents.setDocumentGridRows([]));
      return;
    }
    // Early return if the document list is archived or filter is active
    if (showFilter) {
      await dispatch(actions.documents.setResetDocumentFilterForm());
      await dispatch(actions.documents.setIsSearchFilterVisible(true));
      return;
    }
    // Get and set data rows for non-archived documents
    const customFilter = templates.find((template) => template.idTemplate === idTemplate)?.customFilter;
    const { data: rows } = await services.getDocuments({ idProgram, idUser, idTemplate, allAuthorized }, customFilter);
    dispatch(actions.documents.setDocumentGridRows(rows));
    checkHighPriorityDocs(rows);
  }, [allAuthorized]);

  return useMemo(() => <Routes />, []);
};

export default addHotkeys(Documents, hotActions);
