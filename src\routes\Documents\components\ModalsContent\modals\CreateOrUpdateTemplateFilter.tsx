import { InputContainer } from 'routes/Report/styles';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import Modal from 'components/Modal';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import TextInput from 'components/Input/TextInput';

const InputContainerCenter = styled(InputContainer)`
  justify-content: flex-end;
`;

const CreateOrUpdateTemplateFilter = () => {
  const t = utils.intl.useTranslator();

  const [value, setValue] = useState<string>('');

  const modalProps = useSelector(selectors.modal.getModalProps);

  const { selected, onSaveTemplate, filterValues } = modalProps;

  useEffect(() => {
    if (selected) setValue(selected.description);
  }, [selected]);

  return (
    <>
      <Modal.Header title={t(selected ? 'update-filters-template' : 'save-filters-template')} />
      <Modal.Content>
        <InputContainerCenter>
          <label>{t('setting-name-of-filters')}</label>
          <TextInput value={value} onChange={(e: any) => setValue(e.target.value)} disabled={!!selected} />
        </InputContainerCenter>
      </Modal.Content>
      <Modal.Footer
        confirmAction={() => onSaveTemplate(value, filterValues)}
        confirmText={t('save')}
        confirmDisabled={value.length === 0}
      />
    </>
  );
};
export default CreateOrUpdateTemplateFilter;
