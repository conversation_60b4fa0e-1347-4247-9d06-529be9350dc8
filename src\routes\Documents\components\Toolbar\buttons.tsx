/* eslint max-lines: off */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType, modalActionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';
import ManualReassignContainer from './buttons/ManualReassignContainer';
import { ForceValidationDetail, ForceValidationlist } from './buttons/ForceValidation';
import ApplyAndValidate from './buttons/ApplyAndValidate';
import ConnectJies from './buttons/ConnectJies';
import ExportToSap from './buttons/ExportToSap';
import ReverseRepostButton from './buttons/ReverseRepostButton';
import CheckDuplicateDocument from './buttons/DuplicateDocuments';
import SendToWorkflow from './buttons/SendToWorkflow';
import UnlockDocument from './buttons/UnlockDocument';
import Rebatch from './buttons/Rebatch';
import LitigationMail from './buttons/LitigationMail';
import LitigationArchive from './buttons/LitigationArchive';
import AssignDocument from './buttons/AssignDocument';
import DownloadJies from './buttons/DownloadJies';
import SendMail from './buttons/SendMail';
import Reject from './buttons/Reject';
import Approval from './buttons/Approval';
import AssignDocumentLatency from './buttons/AssignDocumentLatency';
import services from 'services';
import RetriveDocFromLitigationPark from './buttons/RetriveDocFromLitigationPark';
import RetriveDocFromDispatcher from './buttons/RetrieveDocFromDispatcher';
import SanityCheck from './buttons/SanityCheck';
import GenerateDEM from './buttons/GenerateDEM';

interface ToolbarButton {
  action: string;
}

const ToolbarButton = (props: ToolbarButton) => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const { action } = props;
  const records = useSelector(selectors.documents.selectAllActiveDocuments);
  const isSearchFilterVisible = useSelector(selectors.documents.selectIsSearchFilterVisible);
  const isEditView = utils.documents.isOpenDocumentView();
  const activeRows = useSelector(selectors.documents.selectAllActiveRows);

  const deleteIsActive = activeRows.every((row) => row.groupName === 'Validation');

  switch (action) {
    case actionType.MASSIVE_DOC_TYPE_CHANGE:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(actionType.MASSIVE_DOC_TYPE_CHANGE))}
          disabled={!utils.user.isActionEnabled(records, actionType.MASSIVE_DOC_TYPE_CHANGE)}
          scope="tertiary"
          value={t('massive-doc-type-change')}
          icon="circle"
        />
      );
    case actionType.SENDMAIL:
      return <SendMail />;
    case actionType.REASSIGN_PRIORITY:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.REASSIGN_PRIORITY))}
          disabled={!utils.user.isActionEnabled(records, actionType.REASSIGN_PRIORITY)}
          scope="tertiary"
          value={t('reassign-priority')}
          icon="cog"
        />
      );
    case actionType.ALLEGA:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.ALLEGA))}
          disabled={!utils.user.isActionEnabled(records, actionType.ALLEGA)}
          scope="tertiary"
          value={t('manage-attachments')}
          icon="paperclip"
        />
      );
    case actionType.ASSIGNDOCUMENT:
      return <AssignDocument />;
    case actionType.NOTES:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.ADD_MEMO))}
          disabled={!utils.user.isActionEnabled(records, actionType.NOTES)}
          scope="tertiary"
          value={t('notes')}
          icon="sticky-note"
        />
      );
    case actionType.DELETE_DOCUMENT:
      return (
        <CommonButton
          id={actionType.DELETE_DOCUMENT}
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.DELETE_DOCUMENTS))}
          scope="tertiary"
          value={t('delete')}
          icon="trash"
          disabled={!utils.user.isActionEnabled(records, actionType.DELETE_DOCUMENT) || !deleteIsActive}
        />
      );
    case actionType.OPEN_WORKFLOW:
      return <SendToWorkflow />;
    case actionType.RETURN_TO_BATCH:
      return <Rebatch />;
    case actionType.LOG:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.LOG))}
          disabled={!utils.user.isActionEnabled(records, actionType.LOG)}
          scope="tertiary"
          value={t('log')}
          icon="sticky-note"
        />
      );
    case actionType.MANUAL_REASSIGN_DOCUMENTS:
      return (
        <ManualReassignContainer
          disabled={!utils.user.isActionEnabled(records, actionType.MANUAL_REASSIGN_DOCUMENTS)}
          mode={actionType.MANUAL_REASSIGN_DOCUMENTS}
        />
      );

    case actionType.MANUAL_REASSIGN_DOCUMENTS_NO_OCR:
      return (
        <ManualReassignContainer
          disabled={!utils.user.isActionEnabled(records, actionType.MANUAL_REASSIGN_DOCUMENTS_NO_OCR)}
          mode={actionType.MANUAL_REASSIGN_DOCUMENTS_NO_OCR}
        />
      );

    case actionType.MANUAL_REASSIGN_DOCUMENTS_OCR:
      return (
        <ManualReassignContainer
          disabled={!utils.user.isActionEnabled(records, actionType.MANUAL_REASSIGN_DOCUMENTS_OCR)}
          mode={actionType.MANUAL_REASSIGN_DOCUMENTS_OCR}
        />
      );

    case actionType.APPLYRULES:
      return (
        <ApplyAndValidate disabled={!utils.user.isActionEnabled(records, actionType.APPLYRULES)} isValidate={false} />
      );
    case actionType.VALIDATE:
      return <ApplyAndValidate disabled={!utils.user.isActionEnabled(records, actionType.VALIDATE)} isValidate />;
    case 'ActionDocumentForceQC':
      return isEditView ? <ForceValidationDetail /> : <ForceValidationlist />;
    // intereactive export view case
    case actionType.SELF_VALIDATE:
      return (
        <CommonButton
          id={actionType.SELF_VALIDATE}
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.SELF_VALIDATE))}
          scope="tertiary"
          value={t('self-validate')}
          disabled={!utils.user.isActionEnabled(records, actionType.SELF_VALIDATE)}
          icon="address-card"
        />
      );
    case actionType.SPLIT_FIX:
      return (
        <CommonButton
          id={actionType.SPLIT_FIX}
          action={() => dispatch(actions.modal.openModal(actionType.SPLIT_FIX))}
          disabled={!utils.user.isActionEnabled(records, actionType.SPLIT_FIX)}
          scope="tertiary"
          value={t('split-fix')}
          icon="circle"
        />
      );
    case actionType.ADDNEWSTORE:
      return (
        <CommonButton
          disabled={!utils.user.isActionByCompanyActive(actionType.ADDNEWSTORE)}
          value={t('new-store')}
          icon="plus"
          scope="tertiary"
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.NEW_STORE))}
        />
      );
    case actionType.SET_RECEIPT_DATE:
      return (
        <CommonButton
          id={actionType.SET_RECEIPT_DATE}
          action={() => dispatch(actions.modal.openModal(actionType.SET_RECEIPT_DATE))}
          scope="tertiary"
          value={t('Set Scan Date')}
          disabled={!utils.user.isActionEnabled(records, actionType.SET_RECEIPT_DATE)}
          icon="calendar-alt"
        />
      );
    case actionType.COMPANY_DOC_TYPE:
      return (
        <CommonButton
          action={() => {
            dispatch(actions.modal.openModal(modalActionType.documents.COMPANY_DOC_TYPE));
          }}
          scope="tertiary"
          value={t('company-doc-type')}
          disabled={utils.user.isActionEnabled(records, actionType.COMPANY_DOC_TYPE) || records.length < 1}
          icon="circle"
        />
      );
    case actionType.REJECT:
      return <Reject />;
    case actionType.REJECT_PREVIOUS:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(actionType.REJECT_PREVIOUS))}
          scope="tertiary"
          value={t(actionType.REJECT_PREVIOUS)}
          disabled={!utils.user.isActionEnabled(records, actionType.REJECT_PREVIOUS)}
        />
      );
    // convalida documenti
    case actionType.CONFIRM:
      return <Approval />;
    case actionType.PARK:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(actionType.PARK))}
          scope="tertiary"
          value={t('park-document')}
          disabled={!utils.user.isActionEnabled(records, actionType.PARK)}
        />
      );
    case actionType.CLOSEPARK:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(actionType.CLOSEPARK))}
          scope="tertiary"
          value={t('close-park-document')}
          disabled={!utils.user.isActionEnabled(records, actionType.CLOSEPARK)}
        />
      );
    case actionType.LITIGATION_MAIL:
      return <LitigationMail />;
    case actionType.LITIGATION_PARK_MAIL:
      return <LitigationMail isLitigationPark={true} />;
    case actionType.LITIGATION_PRINT:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.LITIGATION, { type: 'print' }))}
          icon="print"
          scope="tertiary"
          value={t(actionType.LITIGATION_PRINT)}
          disabled={!utils.user.isActionEnabled(records, actionType.LITIGATION_PRINT)}
        />
      );
    case actionType.LITIGATION_PARK_PRINT:
      return (
        <CommonButton
          action={() =>
            dispatch(
              actions.modal.openModal(modalActionType.documents.LITIGATION, { type: 'print', isLitigationPark: true }),
            )
          }
          icon="print"
          scope="tertiary"
          value={t(actionType.LITIGATION_PARK_PRINT)}
          disabled={!utils.user.isActionEnabled(records, actionType.LITIGATION_PARK_PRINT)}
        />
      );
    case actionType.LITIGATION_ARCHIVE:
      return <LitigationArchive />;
    case actionType.LITIGATION_PARK_ARCHIVE:
      return <LitigationArchive isLitigationPark={true} />;
    case 'NuovaRicerca':
    case 'NuovaRicercaDocList':
      return (
        <CommonButton
          id="nuova-ricerca"
          scope="tertiary"
          value={t('nuova-ricerca-doc')}
          action={async () => {
            await dispatch(actions.documents.setCurrentFilters([]));
            await dispatch(actions.documents.setCurrentPageIndex(0));
            await dispatch(actions.documents.setResetDocumentFilterForm());
            await dispatch(actions.documents.setSelectedTemplateFilter(null));
            dispatch(actions.documents.setDocumentImage(''));
            dispatch(actions.documents.setActiveDocumentId([]));
            dispatch(actions.documents.setDocumentImageId(0));
            await dispatch(actions.documents.setIsSearchFilterVisible(true));
          }}
        />
      );
    case 'RaffinaRicerca':
    case 'RaffinaRicercaDocList':
      return (
        <CommonButton
          id="raffina-ricerca"
          scope="tertiary"
          value={t('raffina-ricerca-doc')}
          disabled={isSearchFilterVisible}
          action={() => {
            dispatch(actions.documents.setIsSearchFilterVisible(true));
            dispatch(actions.documents.setDocumentGridRows([]));
            dispatch(actions.documents.setDocumentImage(''));
            dispatch(actions.documents.setDocumentImageId(0));
            dispatch(actions.documents.setActiveDocumentId([]));
          }}
        />
      );
    case 'SetSubject':
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.SEARCH_SUBJECT))}
          id="set-subject"
          scope="tertiary"
          disabled={!utils.user.isActionEnabled(records, 'SetSubject')}
          value={t('SetSubject')}
        />
      );
    case actionType.ASK_INFO:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(actionType.ASK_INFO))}
          scope="tertiary"
          value={t('ask_info')}
          disabled={!utils.user.isActionEnabled(records, actionType.ASK_INFO)}
        />
      );
    case actionType.RESPONSE_INFO:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.RESPONSE_INFO))}
          scope="tertiary"
          value={t('convalidate-info')}
          disabled={!utils.user.isActionEnabled(records, actionType.RESPONSE_INFO)}
        />
      );
    case actionType.CLOSE_LITIGATION:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.CLOSE_LITIGATION))}
          scope="tertiary"
          value={t('close_validation')}
          disabled={!utils.user.isActionEnabled(records, actionType.CLOSE_LITIGATION)}
        />
      );
    case actionType.DOWNLOAD_JIES:
      return <DownloadJies />;
    case actionType.CONNECT_JIES:
      return <ConnectJies />;
    case actionType.EXPORT_TO_SAP:
      return <ExportToSap />;
    case actionType.REVERSE_REPOST:
      return <ReverseRepostButton disabled={!utils.user.isActionEnabled(records, actionType.REVERSE_REPOST)} />;
    case actionType.CHECK_DUPLICATE_DOCUMENT:
      return <CheckDuplicateDocument />;
    case actionType.DOCUMENT_HISTORY:
      return (
        <CommonButton
          action={() => dispatch(actions.modal.openModal(modalActionType.documents.DOCUMENT_HISTORY))}
          disabled={!utils.user.isActionEnabled(records, actionType.DOCUMENT_HISTORY)}
          scope="tertiary"
          value={t('document-history-button')}
          icon="sticky-note"
        />
      );
    case actionType.UNLOCK_DOCUMENT:
      return <UnlockDocument disabled={!utils.user.isActionEnabled(records, actionType.UNLOCK_DOCUMENT)} />;
    case actionType.ASSIGN_DOCUMENT_LATENCY:
      return <AssignDocumentLatency />;
    case actionType.DOWNLOAD_PDF:
      return (
        <CommonButton
          action={() => {
            dispatch(
              actions.modal.setModal({
                actionType: modalActionType.documents.DOWNLOAD_ARCHIVE_PDF,
                props: {
                  func: async () => {
                    const response = await services.downloadArchivePDF(records.map((row) => Number(row?.protocol)));
                    if (response?.data?.size === 0) {
                      utils.app.notify('warning', t('size-0-zip'));
                      return;
                    }
                    utils.file.downloadFileZip(response.data, 'archived-pdf.zip');
                    dispatch(actions.modal.closeModal());
                  },
                  title: t('download-pdf'),
                  subtitle: t('download-pdf-subtitle'),
                  confirmText: t('continue-download-pdf'),
                },
              }),
            );
          }}
          disabled={!utils.user.isActionEnabled(records, actionType.DOWNLOAD_PDF)}
          scope="tertiary"
          value={t('download-pdf')}
          icon="download"
        />
      );
    case actionType.RETRIVE_DOC_FROM_LITIGATION:
      return <RetriveDocFromLitigationPark />;
    case actionType.RETRIEVE_FROM_DOCUMENTS:
      return <RetriveDocFromDispatcher />;
    case actionType.SANITY_CHECK:
      return <SanityCheck />;
    case actionType.GENERATE_DEM:
      return <GenerateDEM />;
    default:
      return <div> {action} not supported</div>;
  }
};

export default ToolbarButton;
