import React from 'react';
import <PERSON><PERSON><PERSON>, { PieChartProps } from 'components/PieChart';

import { <PERSON>a, Story } from '@storybook/react';

export default {
  title: 'Pie Chart',
  component: <PERSON><PERSON><PERSON>,
} as Meta;

const data = [
  {
    x: 'Category A',
    y: 20.25,
    percentage: 40,
  },
  {
    x: 'Category B',
    y: 20.75,
    percentage: 40,
  },
  {
    x: 'Category C',
    y: 30.000005,
    percentage: 20,
  },
];

const Template: Story<PieChartProps> = (args) => <PieChart {...args} />;
export const Default = Template.bind({});

Default.args = {
  data,
  colors: ['red', 'blue', 'yellow'],
};
