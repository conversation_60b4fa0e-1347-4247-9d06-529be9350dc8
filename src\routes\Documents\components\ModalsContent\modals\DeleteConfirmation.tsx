import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;

const DeleteConfirmationModal = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const modalProps = useSelector(selectors.modal.getModalProps);

  return (
    <>
      <Header title={modalProps.title ?? t('delete_confirmation')} subtitle={modalProps.subtitle} />
      <ButtonContainer>
        <CommonButton
          value={t('continua')}
          action={() => {
            modalProps.func();
          }}
        />
        <CommonButton
          scope="secondary"
          value={t('annulla')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default DeleteConfirmationModal;
