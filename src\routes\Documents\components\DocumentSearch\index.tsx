/* eslint-disable max-lines */

import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import MultiSelectInput from 'components/Input/MultiSelect';
import TextInput, { InputLabel } from 'components/Input/TextInput';
import { IFilterDinamicForm, ITemplateSavedFilters } from 'models/response';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';
import utils from 'utils';
import DateRange from './components/DateRange';
import CombinedInput from './components/CombinedInput';
import { IFilterValue } from 'models/request';
import {
  getDateValue,
  getInputValue,
  getOptions,
  getSelectedOption,
  getSelectedOptionsMultiSelect,
  handleDateChange,
  handleMultiSelectInputChange,
  handleSelectInputChange,
  handleTextInputChange,
  validateFilter,
} from './utils';
import SelectTemplateFilters from './components/SelectTemplateFilters';
import { SelectTemplateActions } from 'routes/Documents/routes/documentList';

const FiltersWrapper = styled.div<{ clickable?: boolean }>`
  display: flex;
  justify-content: center;
  margin: 50px 25px;
`;

const InputsContainer = styled.div`
  position: relative;
  display: flex;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 25px;
`;

const Container = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.white};
  text-align: 'center';
  overflow-y: 'auto';
  color : ${({ theme }) => theme.colorPalette.grey.grey12};
`;

interface Props {
  filters: IFilterDinamicForm[];
  prePopulatedFilters: IFilterValue[];
  onSearch: (filterValues: IFilterValue[]) => void;
  idLoggedUser: number;
  templateProps: {
    showSavedFilters: boolean;
    templatesFilterSaved: ITemplateSavedFilters[];
    selectedTemplate: ITemplateSavedFilters | null;
    selectTemplateActions: SelectTemplateActions;
  };
}

const DinamicFilter = (props: Props) => {
  const t = utils.intl.useTranslator();

  const { filters, prePopulatedFilters, onSearch, idLoggedUser, templateProps } = props;

  const { showSavedFilters, templatesFilterSaved, selectedTemplate, selectTemplateActions } = templateProps || {};

  const [filterValues, setFilterValues] = useState<IFilterValue[]>([]);
  const [filterErrors, setFilterErrors] = useState<{ [key: string]: string }>({});
  const [resetCounter, setResetCounter] = useState(0);

  const renderInput = (filter: IFilterDinamicForm) => {
    type ErrorsType = { [key: string]: string };

    let errors: ErrorsType = {};

    // Check if filter.filterName is an array and if it contains valid elements
    if (Array.isArray(filter.filterName) && filter.filterName.length > 0) {
      if (filter.filterName.every((name) => typeof name === 'string')) {
        // Create an errors object that maps filter names to errors
        errors = filter.filterName.reduce((acc: ErrorsType, name: string) => {
          if (filterErrors[name]) {
            acc[name] = filterErrors[name];
          }
          return acc;
        }, {});
      }
    }

    // Function to get the error message for a specific filter
    const getErrorMessage = (filterName: string) => {
      return errors[filterName] ? t(errors[filterName]) : undefined;
    };

    // Function to get the aggregated error message
    const getAggregatedErrorMessage = () => {
      // Returns the first error message found among all filter names
      const firstErrorKey = Array.isArray(filter.filterName)
        ? filter.filterName.find((name) => errors[name])
        : undefined;
      return firstErrorKey ? getErrorMessage(firstErrorKey) : undefined;
    };

    // If the input type is 'select' and there are dependent filters
    if (filter.inputType === 'select' && filter.inputProp?.dependentOn?.length) {
      const dependentOn = filter.inputProp.dependentOn;

      // Find the names of dependent filters and filter out undefined results
      const dependentFilterNames = dependentOn
        .map((id) => filters.find((el) => el.idFilter === id)?.filterName)
        .flat() // Appiattisce l'array risultante
        .filter((name): name is string => name !== undefined); // Filtra solo i valori definiti

      // Check if all dependent filters are populated
      const allDependenciesSatisfied = dependentFilterNames.every((name) => {
        return (
          filterValues.length &&
          filterValues.some((fv) => {
            // Check if fv.options is an array and contains an object with the key name
            return (
              Array.isArray(fv.options) &&
              fv.options.some((option) => option[name as keyof typeof option] !== undefined)
            );
          })
        );
      });

      if (!allDependenciesSatisfied) {
        if (filter.inputProp.defaultOptions?.length) {
          // If not all dependent filters are populated, show a status message
          return (
            <Dropdown
              key={filter.idFilter}
              name={filter.label}
              value={getSelectedOption(filter, filter.inputProp.defaultOptions, filterValues)}
              onChange={(option) =>
                handleSelectInputChange(
                  filter,
                  option,
                  filterValues,
                  setFilterValues,
                  filterErrors,
                  filters,
                  setFilterErrors,
                )
              }
              options={filter.inputProp.defaultOptions || []}
              isClearable
              hasFeedback={!!getAggregatedErrorMessage()}
              feedbackMessage={getAggregatedErrorMessage()}
            />
          );
        }
        return (
          <TextInput
            key={filter.idFilter}
            name={filter.label}
            value={getInputValue(filter, filterValues)}
            onChange={(e: any) =>
              handleTextInputChange(
                filter,
                e.target.value,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            borderRadius="3px"
            hasFeedback={!!getAggregatedErrorMessage()}
            feedbackMessage={getAggregatedErrorMessage()}
            type={filter.inputType}
          />
        );
      }
    }

    let dateValues;

    switch (filter.inputType) {
      case 'text':
      case 'number':
        return (
          <TextInput
            key={filter.idFilter}
            name={filter.label}
            onChange={(e: any) =>
              handleTextInputChange(
                filter,
                e.target.value,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            borderRadius="3px"
            value={getInputValue(filter, filterValues)}
            hasFeedback={!!getAggregatedErrorMessage()}
            feedbackMessage={getAggregatedErrorMessage()}
            type={filter.inputType}
          />
        );
      case 'select':
        return (
          <Dropdown
            key={filter.idFilter}
            name={filter.label}
            onChange={(option) =>
              handleSelectInputChange(
                filter,
                option,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            options={getOptions(filter, idLoggedUser, filters, filterValues, t)}
            isClearable
            value={getSelectedOption(filter, getOptions(filter, idLoggedUser, filters, filterValues, t), filterValues)}
            hasFeedback={!!getAggregatedErrorMessage()}
            feedbackMessage={getAggregatedErrorMessage()}
          />
        );
      case 'multiselect':
        return (
          <MultiSelectInput
            key={filter.idFilter}
            labelledBy={filter.label}
            options={getOptions(filter, idLoggedUser, filters, filterValues, t)}
            onChange={(option: any) =>
              handleMultiSelectInputChange(
                filter,
                option,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            value={
              getSelectedOptionsMultiSelect(
                filter,
                getOptions(filter, idLoggedUser, filters, filterValues, t),
                filterValues,
              ) || []
            }
            hasFeedback={!!getAggregatedErrorMessage()}
            feedbackMessage={getAggregatedErrorMessage()}
            style={{ textAlign: 'left' }}
          />
        );
      case 'date':
        dateValues = getDateValue(filter, filterValues);
        return (
          <DateRange
            key={filter.idFilter}
            date={
              dateValues && typeof dateValues === 'object' && !('from' in dateValues) && !('to' in dateValues)
                ? dateValues
                : null
            }
            fromDate={dateValues && typeof dateValues === 'object' && 'from' in dateValues ? dateValues.from : null}
            toDate={dateValues && typeof dateValues === 'object' && 'to' in dateValues ? dateValues.to : null}
            setDate={(date) =>
              handleDateChange(
                filter,
                filter.filterName[0],
                date,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            setFromDate={(date) =>
              handleDateChange(
                filter,
                filter.inputProp?.rangeFilterName?.from || '',
                date,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            setToDate={(date) =>
              handleDateChange(
                filter,
                filter.inputProp?.rangeFilterName?.to || '',
                date,
                filterValues,
                setFilterValues,
                filterErrors,
                filters,
                setFilterErrors,
              )
            }
            label={t(filter.label)}
            isCheckboxVisible={filter.inputProp?.enableDateRange || false}
            hasFeedback={!!getAggregatedErrorMessage()}
            feedbackMessage={getAggregatedErrorMessage()}
          />
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    const updateFilterValues = () => {
      if (!prePopulatedFilters || Object.keys(prePopulatedFilters).length === 0) {
        // reset if no prepopulated filters are present
        setFilterValues([]);
        setResetCounter((prev) => prev + 1); // TRIGGER RESET
        return;
      }

      // rebuild filterValues from prePopulatedFilters
      const updatedFilterValues: IFilterValue[] = filters
        .map((filter) => {
          const dynamicValue = Array.isArray(prePopulatedFilters)
            ? prePopulatedFilters.find((fv: any) => fv.idFilter === filter.idFilter)?.options || []
            : [];

          return { idFilter: filter.idFilter, options: dynamicValue };
        })
        .filter((fv) => fv.options.length > 0); // Solo i filtri con opzioni valide

      // Update the state only if the values are actually different
      setFilterValues((prev) => {
        if (JSON.stringify(prev) !== JSON.stringify(updatedFilterValues)) {
          return updatedFilterValues;
        }
        return prev;
      });
    };

    const handleResetAndDisable = () => {
      updateFilterValues();
      setResetCounter((prev) => prev + 1); // TRIGGER RESET
    };

    const button = document.getElementById('nuova-ricerca');
    if (button) {
      button.addEventListener('click', handleResetAndDisable);
    }

    // Update the filters on first mount and whenever prePopulatedFilters or filters change
    updateFilterValues();

    // Cleanup
    return () => {
      if (button) {
        button.removeEventListener('click', handleResetAndDisable);
      }
    };
  }, [prePopulatedFilters, filters]);

  const isVisibleConditionSatisfied = (filter: IFilterDinamicForm) => {
    const { visibleCondition } = filter.inputProp || {};

    if (!visibleCondition) return true;

    const { dependentOn, value } = visibleCondition;

    const dependentFilter = filterValues.find((fv) => fv.idFilter === dependentOn);

    if (!dependentFilter || !dependentFilter.options || dependentFilter.options.length === 0) {
      return false;
    }

    // Checks that the number of options is equal to the number of values in `visibleCondition`
    if (dependentFilter.options.length !== value.length) {
      return false;
    }

    // Checks if all the values match
    const isValueSatisfied = dependentFilter.options.every((option) => {
      return Object.keys(option).some((key) => {
        const optionValue = option[key];

        // Checks if `optionValue` and `value` are exactly the same
        if (Array.isArray(optionValue)) {
          // Both must contain exactly the same number of elements and have the same values
          return optionValue.length === value.length && optionValue.every((val, index) => val === value[index]);
        }

        // If `optionValue` is not an array, check if the value is equal to the one in `value`
        return value.includes(optionValue);
      });
    });

    return isValueSatisfied;
  };

  return (
    <Container style={{ textAlign: 'center', overflowY: 'auto' }}>
      {filters.length ? (
        <>
          {showSavedFilters && (
            <SelectTemplateFilters
              list={templatesFilterSaved}
              selected={selectedTemplate}
              filterValues={filterValues}
              selectTemplateActions={selectTemplateActions}
              onResetFilters={() => setResetCounter((prev) => prev + 1)}
            />
          )}
          <FiltersWrapper>
            <div style={{ marginRight: '30px' }}>
              {filters.map((filter) => {
                if (!filter.inputProp?.isInputCombined) {
                  if (filter.inputProp?.visibleCondition && !isVisibleConditionSatisfied(filter)) return null;
                  if (filter.inputType === 'date') {
                    return <InputsContainer key={filter.idFilter}>{renderInput(filter)}</InputsContainer>;
                  }
                  return (
                    <InputsContainer key={filter.idFilter}>
                      <InputLabel>{t(filter.label)}</InputLabel>
                      {renderInput(filter)}
                    </InputsContainer>
                  );
                }
                return null;
              })}
            </div>
            <div>
              {filters.map((filter) => {
                if (filter.inputProp?.isInputCombined) {
                  if (filter.inputProp?.visibleCondition && !isVisibleConditionSatisfied(filter)) return null;
                  return (
                    <CombinedInput
                      key={`${filter.idFilter}_${resetCounter}`}
                      filter={filter}
                      setFilterValues={setFilterValues}
                      filterErrors={filterErrors}
                      validateFilter={(filter, updatedValues, combinedValue) =>
                        validateFilter(
                          filter,
                          updatedValues,
                          filterErrors,
                          filters,
                          filterValues,
                          setFilterErrors,
                          combinedValue,
                        )
                      }
                      defaultValue={
                        prePopulatedFilters.length
                          ? prePopulatedFilters.find((el) => el.idFilter === filter.idFilter)?.options[0]
                          : undefined
                      }
                    />
                  );
                }
                return null;
              })}
            </div>
          </FiltersWrapper>
          <CommonButton
            value={t('search_document')}
            action={() => onSearch(filterValues)}
            disabled={Object.keys(filterErrors).length > 0}
          />
        </>
      ) : (
        <Container style={{ margin: '50px 0px' }}> {t('filters_not_present')} </Container>
      )}
    </Container>
  );
};
export default DinamicFilter;
