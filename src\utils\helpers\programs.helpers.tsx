import services from 'services';
import useSWR from 'swr';

export const useProgramsTemplateSavedFilters = (
  idProgram: number,
  idTemplate: number | null,
  isSearchVisible: boolean,
  showSavedFilters: boolean,
) => {
  // Creates a unique key using idProgram and idTemplate if idTemplate is not null,
  // and if isSearchVisible (the visibility of the filters) is true,
  // and showSavedFilters (the visibility of the saved filter templates) is also true
  const key =
    idTemplate !== null && isSearchVisible && showSavedFilters ? `${idProgram}-${idTemplate}-savedFilters` : null;

  const { data, error } = useSWR(key, () => services.getProgramsTemplateSavedFilters(idProgram, idTemplate));
  const isLoadingTemplates = !data && !error;

  return { data: data?.data ?? [], isLoadingTemplates };
};
