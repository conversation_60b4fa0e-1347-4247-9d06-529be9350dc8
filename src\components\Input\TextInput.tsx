/* eslint-disable max-lines */
import React, { useEffect, useRef, useState } from 'react';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { colorPalette, statusColors } from 'utils/styleConstants';
import styled from 'styled-components/macro';
import { useUpdateEffect } from 'react-use';

const InputWrapper = styled.div<{ justifyContent?: string; fullWidth: boolean }>`
  display: flex;
  align-items: center;
  justify-content: ${({ justifyContent }) => justifyContent ?? 'center'};
  ${(props) => props.fullWidth && 'width: 100%'};
`;

const InputContainer = styled.div<{ fullWidth: boolean }>`
  position: relative;
  ${(props) => props.fullWidth && 'width: 100%'};
`;

const ShowPass = styled.div<{ tall?: boolean }>`
  position: absolute;
  right: 5px;
  top: ${(props) => (props.tall ? '10px' : '5px')};
  color: ${({ theme }) => theme.colorPalette.grey.grey5};
`;

export const InputLabel = styled.label<{
  marginRight?: string;
  labelFontSize?: string;
  labelColor?: string;
  labelWidth?: string;
  textAlign?: string;
}>`
  display: inline-block;
  margin-right: ${({ marginRight }) => (marginRight ? marginRight : '15px')};
  font-size: ${({ theme, labelFontSize }) => labelFontSize ?? theme.fontSizePalette.body.XXS};
  width: ${({ labelWidth }) => labelWidth ?? '80px'};
  text-align: ${({ textAlign }) => textAlign ?? 'right'};
  color: ${({ theme, labelColor }) => labelColor ?? theme.colorPalette.black};
`;

interface StyledInput {
  hasFeedback: boolean;
  feedbackColor?: string;
  noBorder: boolean;
  fontSize?: string | null;
  paddingRight: boolean;
  tall?: boolean;
  fullWidth?: boolean;
  width?: string;
  backgroundColor?: string;
  height?: string;
  borderRadius?: string;
  fontColor?: string;
  fontWeight?: string;
  margin?: string;
}

const Input = styled.input<StyledInput>`
  font-family: Roboto;
  margin: ${({ margin }) => (margin ? margin : 'none')};
  color: ${({ theme, fontColor, disabled }) =>
    disabled ? theme.colorPalette.grey.grey12 : fontColor ? fontColor : theme.colorPalette.black};
  background-color: ${({ theme, backgroundColor }) =>
    backgroundColor
      ? backgroundColor
      : theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey3
      : theme.colorPalette.white};
  font-weight: ${({ theme, fontWeight }) => fontWeight || theme.fontWeightPalette.regular};
  height: ${({ tall, height }) => (height ? `${height}` : tall ? '35px' : '25px')};
  width: ${({ fullWidth, width }) => (fullWidth ? '100%' : width)};
  border: ${({ hasFeedback, theme, noBorder, feedbackColor }) =>
    hasFeedback
      ? `1px solid ${feedbackColor ? feedbackColor : statusColors.error}`
      : noBorder
      ? 'none'
      : `1px solid ${theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey5}`};
  border-radius: ${({ borderRadius }) => `${borderRadius}` || '3px'};
  padding-left: ${({ noBorder }) => (noBorder ? '0' : '5px')};
  padding-right: ${({ paddingRight }) => (paddingRight ? '25px' : '0')};
  ${({ fontSize }) => `font-size: ${fontSize ? fontSize : 12}px`};

  &::placeholder {
    color: ${({ theme }) => theme.colorPalette.grey.grey7};
  }

  &:focus {
    outline: 0;
    border: ${({ hasFeedback, noBorder, theme, feedbackColor }) =>
      hasFeedback
        ? `1px solid ${feedbackColor}`
        : noBorder
        ? 'none'
        : `1px solid ${theme.colorPalette.turquoise.normal}`};
  }

  &:disabled {
    border: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${colorPalette.grey.grey5}`)};
    background-color: ${({ theme }) => theme.colorPalette.grey.grey12};
    color: ${({ theme }) => theme.colorPalette.grey.grey12};
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.grey.grey3}!important;
    opacity: 0.7;

  }

  ::-ms-reveal,
  ::-ms-clear {
    display: none;
  }
`;

export const FeedbackText = styled.p<{
  feedbackColor: string | undefined;
  displayText?: string;
  textPositioning?: string;
}>`
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor }) => (feedbackColor ? feedbackColor : statusColors.error)} !important;
  margin: 1px 0 0 1px;
  text-align: right;
  position: absolute;
  right: 0;
  white-space: nowrap;
  padding-top: 3px;
  ${({ displayText }) => displayText && `display: ${displayText}`};
  ${({ textPositioning }) => textPositioning && `position: ${textPositioning}`};
`;

export interface TextInputProps {
  type?: string;
  label?: string;
  title?: string;
  tall?: boolean;
  onChange?: Function;
  value?: string | number;
  placeholder?: string;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  feedbackColor?: string;
  disabled?: boolean;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  focus?: boolean;
  id?: string;
  name?: string;
  noBorder?: boolean;
  fontSize?: string;
  fullWidth?: boolean;
  width?: string;
  displayText?: string;
  textPositioning?: string;
  backgroundColor?: string;
  height?: string;
  borderRadius?: string;
  fontColor?: string;
  fontWeight?: string;
  margin?: string;
  labelMarginRight?: string;
  labelFontSize?: string;
  labelWidth?: string;
  labelColor?: string;
  maxLength?: number;
  autoComplete?: string;
  justifyContent?: string;
  className?: string;
  min?: number;
  max?: number;
}

const TextInput = ({
  id,
  type = 'text',
  label,
  tall,
  onBlur,
  onFocus,
  onKeyDown,
  noBorder,
  focus,
  onChange,
  value = '',
  placeholder = '',
  title = '',
  hasFeedback = false,
  feedbackMessage = '',
  feedbackColor,
  fullWidth = false,
  name,
  fontSize,
  disabled = false,
  width = '165px',
  displayText,
  textPositioning = 'absolute',
  backgroundColor,
  height,
  borderRadius,
  fontColor,
  fontWeight,
  margin,
  labelMarginRight,
  labelFontSize,
  labelWidth,
  labelColor,
  maxLength,
  autoComplete,
  justifyContent,
  className,
  min,
  max,
}: TextInputProps) => {
  const [currentValue, setCurrentValue] = useState(value);
  const [inputType, setInputType] = useState<string>(type);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentValue(e.target.value);
    onChange && onChange(e);
  };

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  useUpdateEffect(() => {
    if (inputRef.current && focus) {
      inputRef.current.focus();
    }
  }, [focus]);

  return (
    <InputWrapper justifyContent={justifyContent} fullWidth={fullWidth}>
      {label && (
        <InputLabel
          title={label}
          marginRight={labelMarginRight}
          labelFontSize={labelFontSize}
          labelWidth={labelWidth}
          labelColor={labelColor}
        >
          {label}
        </InputLabel>
      )}
      <InputContainer fullWidth={fullWidth}>
        <Input
          ref={inputRef}
          fullWidth={fullWidth}
          width={width}
          tall={tall}
          fontSize={fontSize ? fontSize : null}
          noBorder={noBorder ? noBorder : false}
          id={id}
          name={name}
          title={title}
          onFocus={onFocus}
          onBlur={onBlur}
          onKeyDown={onKeyDown}
          hasFeedback={hasFeedback}
          feedbackColor={feedbackColor}
          disabled={disabled}
          placeholder={placeholder}
          value={currentValue}
          onChange={handleChange}
          type={inputType}
          paddingRight={type === 'password' ? true : false}
          backgroundColor={backgroundColor}
          height={height}
          borderRadius={borderRadius}
          fontColor={fontColor}
          fontWeight={fontWeight}
          margin={margin}
          maxLength={maxLength}
          autoComplete={autoComplete}
          className={className}
          min={min}
          max={max}
        />
        {type === 'password' ? (
          <ShowPass tall={tall}>
            {inputType === 'password' ? (
              <FontAwesomeIcon
                style={{
                  color: colorPalette.grey.grey5,
                  cursor: 'pointer',
                }}
                icon="eye"
                onClick={() => setInputType('text')}
              />
            ) : (
              <FontAwesomeIcon
                style={{
                  color: colorPalette.grey.grey5,
                  cursor: 'pointer',
                }}
                icon="eye-slash"
                onClick={() => setInputType('password')}
              />
            )}
          </ShowPass>
        ) : null}
        {hasFeedback && feedbackMessage.length > 0 && (
          <FeedbackText displayText={displayText} feedbackColor={feedbackColor} textPositioning={textPositioning}>
            {feedbackMessage}
          </FeedbackText>
        )}
      </InputContainer>
    </InputWrapper>
  );
};

export default TextInput;
