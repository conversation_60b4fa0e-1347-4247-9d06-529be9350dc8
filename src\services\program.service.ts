import axios from 'axios';
import { ICreateTemplateSavedFilters } from 'models/request';
import { IFilterDinamicForm, ITemplateSavedFilters, MenuPrograms } from 'models/response';
import { trackPromise } from 'react-promise-tracker';
import useSWR from 'swr';
import { PROGRAMSERVICE } from 'utils/constants';

const getUserPrograms = (idUser: number) =>
  trackPromise(axios.post(`${PROGRAMSERVICE}/getUserPrograms`, { idUser }), 'menu');

/**
 * This API returns the data menu by idUser param
 * @param {number} idUser
 * @return {Promise<AxiosResponse<MenuPrograms>>}
 */
const getMenuPrograms = (idUser: number) =>
  trackPromise(
    axios.post<MenuPrograms>(`${PROGRAMSERVICE}/getUserProgramsWithStructure`, { idUser, language: 'it' }),
    'menu',
  );

/**
 * This API returns the list of filters for the filters screen by idProgram and idTemplate param
 * @param {number} idProgram
 *  @param {number} idTemplate
 * @return {Promise<AxiosResponse<IFilterDinamicForm[]>>}
 */

const getProgramsWithFilters = (idProgram: number, idTemplate: number | null) =>
  trackPromise(
    axios.post<IFilterDinamicForm[]>(`${PROGRAMSERVICE}/getProgramsWithFilters`, { idProgram, idTemplate }),
    'module',
  );

// SWR that uses the getProgramsWithFilters API
const useProgramsWithFilters = (idProgram: number, idTemplate: number | null, isSearchVisible: boolean) => {
  // Creates a unique key using idProgram and idTemplate if idTemplate is not null and
  // if isSearchVisible (the filter panel visibility) is true
  const key = idTemplate !== null && isSearchVisible ? `${idProgram}-${idTemplate}` : null;
  const { data, error } = useSWR(key, () => getProgramsWithFilters(idProgram, idTemplate));
  const isLoading = !data && !error;
  return { data: data ? data.data : [], isLoading };
};

const getProgramsTemplateSavedFilters = (idProgram: number, idTemplate: number | null) =>
  trackPromise(
    axios.get<ITemplateSavedFilters[]>(`${PROGRAMSERVICE}/getProgramsTemplateSavedFilters`, {
      params: { idProgram, idTemplate },
    }),
    'module',
  );

const createProgramsTemplateSavedFilters = (body: ICreateTemplateSavedFilters) =>
  trackPromise(axios.post<number>(`${PROGRAMSERVICE}/createProgramsTemplateSavedFilters`, body), 'module');

const updateProgramsTemplateSavedFilters = (body: ITemplateSavedFilters) =>
  trackPromise(
    axios.post<ITemplateSavedFilters>(`${PROGRAMSERVICE}/updateProgramsTemplateSavedFilters`, body),
    'module',
  );

const deleteProgramsTemplateSavedFilters = (idSavedFilter: number) =>
  trackPromise(axios.post(`${PROGRAMSERVICE}/deleteProgramsTemplateSavedFilters`, { idSavedFilter }), 'module');

export default {
  getUserPrograms,
  getMenuPrograms,
  useProgramsWithFilters,
  createProgramsTemplateSavedFilters,
  deleteProgramsTemplateSavedFilters,
  getProgramsTemplateSavedFilters,
  updateProgramsTemplateSavedFilters,
};
